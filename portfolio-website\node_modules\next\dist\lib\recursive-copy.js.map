{"version": 3, "sources": ["../../src/lib/recursive-copy.ts"], "sourcesContent": ["import path from 'path'\nimport type { Dirent, Stats } from 'fs'\nimport { promises, constants } from 'fs'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport isError from './is-error'\n\nconst COPYFILE_EXCL = constants.COPYFILE_EXCL\n\nexport async function recursiveCopy(\n  source: string,\n  dest: string,\n  {\n    concurrency = 32,\n    overwrite = false,\n    filter = () => true,\n  }: {\n    concurrency?: number\n    overwrite?: boolean\n    filter?(filePath: string): boolean\n  } = {}\n): Promise<void> {\n  const cwdPath = process.cwd()\n  const from = path.resolve(cwdPath, source)\n  const to = path.resolve(cwdPath, dest)\n\n  const sema = new Sema(concurrency)\n\n  // deep copy the file/directory\n  async function _copy(item: string, lstats?: Stats | Dirent): Promise<void> {\n    const target = item.replace(from, to)\n\n    await sema.acquire()\n\n    if (!lstats) {\n      // after lock on first run\n      lstats = await promises.lstat(from)\n    }\n\n    // readdir & lstat do not follow symbolic links\n    // if part is a symbolic link, follow it with stat\n    let isFile = lstats.isFile()\n    let isDirectory = lstats.isDirectory()\n    if (lstats.isSymbolicLink()) {\n      const stats = await promises.stat(item)\n      isFile = stats.isFile()\n      isDirectory = stats.isDirectory()\n    }\n\n    if (isDirectory) {\n      try {\n        await promises.mkdir(target, { recursive: true })\n      } catch (err) {\n        // do not throw `folder already exists` errors\n        if (isError(err) && err.code !== 'EEXIST') {\n          throw err\n        }\n      }\n      sema.release()\n      const files = await promises.readdir(item, { withFileTypes: true })\n      await Promise.all(\n        files.map((file) => _copy(path.join(item, file.name), file))\n      )\n    } else if (\n      isFile &&\n      // before we send the path to filter\n      // we remove the base path (from) and replace \\ by / (windows)\n      filter(item.replace(from, '').replace(/\\\\/g, '/'))\n    ) {\n      await promises\n        .copyFile(item, target, overwrite ? undefined : COPYFILE_EXCL)\n        .catch((err) => {\n          // if overwrite is false we shouldn't fail on EEXIST\n          if (err.code !== 'EEXIST') {\n            throw err\n          }\n        })\n      sema.release()\n    } else {\n      sema.release()\n    }\n  }\n\n  await _copy(from)\n}\n"], "names": ["recursiveCopy", "COPYFILE_EXCL", "constants", "source", "dest", "concurrency", "overwrite", "filter", "cwdPath", "process", "cwd", "from", "path", "resolve", "to", "sema", "<PERSON><PERSON>", "_copy", "item", "lstats", "target", "replace", "acquire", "promises", "lstat", "isFile", "isDirectory", "isSymbolicLink", "stats", "stat", "mkdir", "recursive", "err", "isError", "code", "release", "files", "readdir", "withFileTypes", "Promise", "all", "map", "file", "join", "name", "copyFile", "undefined", "catch"], "mappings": ";;;;+BAQsBA;;;eAAAA;;;6DARL;oBAEmB;2BACf;gEACD;;;;;;AAEpB,MAAMC,gBAAgBC,aAAS,CAACD,aAAa;AAEtC,eAAeD,cACpBG,MAAc,EACdC,IAAY,EACZ,EACEC,cAAc,EAAE,EAChBC,YAAY,KAAK,EACjBC,SAAS,IAAM,IAAI,EAKpB,GAAG,CAAC,CAAC;IAEN,MAAMC,UAAUC,QAAQC,GAAG;IAC3B,MAAMC,OAAOC,aAAI,CAACC,OAAO,CAACL,SAASL;IACnC,MAAMW,KAAKF,aAAI,CAACC,OAAO,CAACL,SAASJ;IAEjC,MAAMW,OAAO,IAAIC,eAAI,CAACX;IAEtB,+BAA+B;IAC/B,eAAeY,MAAMC,IAAY,EAAEC,MAAuB;QACxD,MAAMC,SAASF,KAAKG,OAAO,CAACV,MAAMG;QAElC,MAAMC,KAAKO,OAAO;QAElB,IAAI,CAACH,QAAQ;YACX,0BAA0B;YAC1BA,SAAS,MAAMI,YAAQ,CAACC,KAAK,CAACb;QAChC;QAEA,+CAA+C;QAC/C,kDAAkD;QAClD,IAAIc,SAASN,OAAOM,MAAM;QAC1B,IAAIC,cAAcP,OAAOO,WAAW;QACpC,IAAIP,OAAOQ,cAAc,IAAI;YAC3B,MAAMC,QAAQ,MAAML,YAAQ,CAACM,IAAI,CAACX;YAClCO,SAASG,MAAMH,MAAM;YACrBC,cAAcE,MAAMF,WAAW;QACjC;QAEA,IAAIA,aAAa;YACf,IAAI;gBACF,MAAMH,YAAQ,CAACO,KAAK,CAACV,QAAQ;oBAAEW,WAAW;gBAAK;YACjD,EAAE,OAAOC,KAAK;gBACZ,8CAA8C;gBAC9C,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,UAAU;oBACzC,MAAMF;gBACR;YACF;YACAjB,KAAKoB,OAAO;YACZ,MAAMC,QAAQ,MAAMb,YAAQ,CAACc,OAAO,CAACnB,MAAM;gBAAEoB,eAAe;YAAK;YACjE,MAAMC,QAAQC,GAAG,CACfJ,MAAMK,GAAG,CAAC,CAACC,OAASzB,MAAML,aAAI,CAAC+B,IAAI,CAACzB,MAAMwB,KAAKE,IAAI,GAAGF;QAE1D,OAAO,IACLjB,UACA,oCAAoC;QACpC,8DAA8D;QAC9DlB,OAAOW,KAAKG,OAAO,CAACV,MAAM,IAAIU,OAAO,CAAC,OAAO,OAC7C;YACA,MAAME,YAAQ,CACXsB,QAAQ,CAAC3B,MAAME,QAAQd,YAAYwC,YAAY7C,eAC/C8C,KAAK,CAAC,CAACf;gBACN,oDAAoD;gBACpD,IAAIA,IAAIE,IAAI,KAAK,UAAU;oBACzB,MAAMF;gBACR;YACF;YACFjB,KAAKoB,OAAO;QACd,OAAO;YACLpB,KAAKoB,OAAO;QACd;IACF;IAEA,MAAMlB,MAAMN;AACd", "ignoreList": [0]}
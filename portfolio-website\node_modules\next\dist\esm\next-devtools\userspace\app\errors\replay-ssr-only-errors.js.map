{"version": 3, "sources": ["../../../../../src/next-devtools/userspace/app/errors/replay-ssr-only-errors.tsx"], "sourcesContent": ["import { useEffect } from 'react'\nimport { handleClientError } from './use-error-handler'\nimport { isNextRouterError } from '../../../../client/components/is-next-router-error'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../../../shared/lib/errors/constants'\n\nfunction readSsrError(): (Error & { digest?: string }) | null {\n  if (typeof document === 'undefined') {\n    return null\n  }\n\n  const ssrErrorTemplateTag = document.querySelector(\n    'template[data-next-error-message]'\n  )\n  if (ssrErrorTemplateTag) {\n    const message: string = ssrErrorTemplateTag.getAttribute(\n      'data-next-error-message'\n    )!\n    const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack')\n    const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest')\n    const error = new Error(message)\n    if (digest) {\n      ;(error as any).digest = digest\n    }\n    // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n    if (isNextRouterError(error)) {\n      return null\n    }\n    error.stack = stack || ''\n    return error\n  }\n\n  return null\n}\n\n/**\n * Needs to be in the same error boundary as the shell.\n * If it commits, we know we recovered from an SSR error.\n * If it doesn't commit, we errored again and React will take care of error reporting.\n */\nexport function ReplaySsrOnlyErrors({\n  onBlockingError,\n}: {\n  onBlockingError: () => void\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to read during render. The attributes will be gone after commit.\n    const ssrError = readSsrError()\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      if (ssrError !== null) {\n        // TODO(veil): Include original Owner Stack (NDX-905)\n        // TODO(veil): Mark as recoverable error\n        // TODO(veil): console.error\n        handleClientError(ssrError)\n\n        // If it's missing root tags, we can't recover, make it blocking.\n        if (ssrError.digest === MISSING_ROOT_TAGS_ERROR) {\n          onBlockingError()\n        }\n      }\n    }, [ssrError, onBlockingError])\n  }\n\n  return null\n}\n"], "names": ["useEffect", "handleClientError", "isNextRouterError", "MISSING_ROOT_TAGS_ERROR", "readSsrError", "document", "ssrErrorTemplateTag", "querySelector", "message", "getAttribute", "stack", "digest", "error", "Error", "ReplaySsrOnlyErrors", "onBlockingError", "process", "env", "NODE_ENV", "ssrError"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SAASC,iBAAiB,QAAQ,qDAAoD;AACtF,SAASC,uBAAuB,QAAQ,0CAAyC;AAEjF,SAASC;IACP,IAAI,OAAOC,aAAa,aAAa;QACnC,OAAO;IACT;IAEA,MAAMC,sBAAsBD,SAASE,aAAa,CAChD;IAEF,IAAID,qBAAqB;QACvB,MAAME,UAAkBF,oBAAoBG,YAAY,CACtD;QAEF,MAAMC,QAAQJ,oBAAoBG,YAAY,CAAC;QAC/C,MAAME,SAASL,oBAAoBG,YAAY,CAAC;QAChD,MAAMG,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;QAC/B,IAAIG,QAAQ;;YACRC,MAAcD,MAAM,GAAGA;QAC3B;QACA,yFAAyF;QACzF,IAAIT,kBAAkBU,QAAQ;YAC5B,OAAO;QACT;QACAA,MAAMF,KAAK,GAAGA,SAAS;QACvB,OAAOE;IACT;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASE,oBAAoB,KAInC;IAJmC,IAAA,EAClCC,eAAe,EAGhB,GAJmC;IAKlC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,wEAAwE;QACxE,MAAMC,WAAWf;QACjB,sDAAsD;QACtDJ,UAAU;YACR,IAAImB,aAAa,MAAM;gBACrB,qDAAqD;gBACrD,wCAAwC;gBACxC,4BAA4B;gBAC5BlB,kBAAkBkB;gBAElB,iEAAiE;gBACjE,IAAIA,SAASR,MAAM,KAAKR,yBAAyB;oBAC/CY;gBACF;YACF;QACF,GAAG;YAACI;YAAUJ;SAAgB;IAChC;IAEA,OAAO;AACT", "ignoreList": [0]}
{"version": 3, "sources": ["../../../src/server/lib/patch-fetch.ts"], "sourcesContent": ["import type {\n  WorkAsyncStorage,\n  WorkStore,\n} from '../app-render/work-async-storage.external'\n\nimport { AppRenderSpan, NextNodeServerSpan } from './trace/constants'\nimport { getTracer, SpanKind } from './trace/tracer'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  NEXT_CACHE_TAG_MAX_ITEMS,\n  NEXT_CACHE_TAG_MAX_LENGTH,\n} from '../../lib/constants'\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FetchMetric } from '../base-http'\nimport { createDedupeFetch } from './dedupe-fetch'\nimport type { WorkUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../response-cache'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport { cloneResponse } from './clone-response'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\ntype Fetcher = typeof fetch\n\ntype PatchedFetcher = Fetcher & {\n  readonly __nextPatched: true\n  readonly __nextGetStaticStore: () => WorkAsyncStorage\n  readonly _nextOriginalFetch: Fetcher\n}\n\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch')\n\nfunction isFetchPatched() {\n  return (globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] === true\n}\n\nexport function validateRevalidate(\n  revalidateVal: unknown,\n  route: string\n): undefined | number {\n  try {\n    let normalizedRevalidate: number | undefined = undefined\n\n    if (revalidateVal === false) {\n      normalizedRevalidate = INFINITE_CACHE\n    } else if (\n      typeof revalidateVal === 'number' &&\n      !isNaN(revalidateVal) &&\n      revalidateVal > -1\n    ) {\n      normalizedRevalidate = revalidateVal\n    } else if (typeof revalidateVal !== 'undefined') {\n      throw new Error(\n        `Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`\n      )\n    }\n    return normalizedRevalidate\n  } catch (err: any) {\n    // handle client component error from attempting to check revalidate value\n    if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n      throw err\n    }\n    return undefined\n  }\n}\n\nexport function validateTags(tags: any[], description: string) {\n  const validTags: string[] = []\n  const invalidTags: Array<{\n    tag: any\n    reason: string\n  }> = []\n\n  for (let i = 0; i < tags.length; i++) {\n    const tag = tags[i]\n\n    if (typeof tag !== 'string') {\n      invalidTags.push({ tag, reason: 'invalid type, must be a string' })\n    } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n      invalidTags.push({\n        tag,\n        reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`,\n      })\n    } else {\n      validTags.push(tag)\n    }\n\n    if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n      console.warn(\n        `Warning: exceeded max tag count for ${description}, dropped tags:`,\n        tags.slice(i).join(', ')\n      )\n      break\n    }\n  }\n\n  if (invalidTags.length > 0) {\n    console.warn(`Warning: invalid tags passed to ${description}: `)\n\n    for (const { tag, reason } of invalidTags) {\n      console.log(`tag: \"${tag}\" ${reason}`)\n    }\n  }\n  return validTags\n}\n\nfunction trackFetchMetric(\n  workStore: WorkStore,\n  ctx: Omit<FetchMetric, 'end' | 'idx'>\n) {\n  // If the static generation store is not available, we can't track the fetch\n  if (!workStore) return\n  if (workStore.requestEndedState?.ended) return\n\n  const isDebugBuild =\n    (!!process.env.NEXT_DEBUG_BUILD ||\n      process.env.NEXT_SSG_FETCH_METRICS === '1') &&\n    workStore.isStaticGeneration\n  const isDevelopment = process.env.NODE_ENV === 'development'\n\n  if (\n    // The only time we want to track fetch metrics outside of development is when\n    // we are performing a static generation & we are in debug mode.\n    !isDebugBuild &&\n    !isDevelopment\n  ) {\n    return\n  }\n\n  workStore.fetchMetrics ??= []\n\n  workStore.fetchMetrics.push({\n    ...ctx,\n    end: performance.timeOrigin + performance.now(),\n    idx: workStore.nextFetchId || 0,\n  })\n}\n\ninterface PatchableModule {\n  workAsyncStorage: WorkAsyncStorage\n  workUnitAsyncStorage: WorkUnitAsyncStorage\n}\n\nexport function createPatchedFetcher(\n  originFetch: Fetcher,\n  { workAsyncStorage, workUnitAsyncStorage }: PatchableModule\n): PatchedFetcher {\n  // Create the patched fetch function.\n  const patched = async function fetch(\n    input: RequestInfo | URL,\n    init: RequestInit | undefined\n  ): Promise<Response> {\n    let url: URL | undefined\n    try {\n      url = new URL(input instanceof Request ? input.url : input)\n      url.username = ''\n      url.password = ''\n    } catch {\n      // Error caused by malformed URL should be handled by native fetch\n      url = undefined\n    }\n    const fetchUrl = url?.href ?? ''\n    const method = init?.method?.toUpperCase() || 'GET'\n\n    // Do create a new span trace for internal fetches in the\n    // non-verbose mode.\n    const isInternal = (init?.next as any)?.internal === true\n    const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1'\n    // We don't track fetch metrics for internal fetches\n    // so it's not critical that we have a start time, as it won't be recorded.\n    // This is to workaround a flaky issue where performance APIs might\n    // not be available and will require follow-up investigation.\n    const fetchStart: number | undefined = isInternal\n      ? undefined\n      : performance.timeOrigin + performance.now()\n\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // During static generation we track cache reads so we can reason about when they fill\n    let cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n\n    const result = getTracer().trace(\n      isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch,\n      {\n        hideSpan,\n        kind: SpanKind.CLIENT,\n        spanName: ['fetch', method, fetchUrl].filter(Boolean).join(' '),\n        attributes: {\n          'http.url': fetchUrl,\n          'http.method': method,\n          'net.peer.name': url?.hostname,\n          'net.peer.port': url?.port || undefined,\n        },\n      },\n      async () => {\n        // If this is an internal fetch, we should not do any special treatment.\n        if (isInternal) {\n          return originFetch(input, init)\n        }\n\n        // If the workStore is not available, we can't do any\n        // special treatment of fetch, therefore fallback to the original\n        // fetch implementation.\n        if (!workStore) {\n          return originFetch(input, init)\n        }\n\n        // We should also fallback to the original fetch implementation if we\n        // are in draft mode, it does not constitute a static generation.\n        if (workStore.isDraftMode) {\n          return originFetch(input, init)\n        }\n\n        const isRequestInput =\n          input &&\n          typeof input === 'object' &&\n          typeof (input as Request).method === 'string'\n\n        const getRequestMeta = (field: string) => {\n          // If request input is present but init is not, retrieve from input first.\n          const value = (init as any)?.[field]\n          return value || (isRequestInput ? (input as any)[field] : null)\n        }\n\n        let finalRevalidate: number | undefined = undefined\n        const getNextField = (field: 'revalidate' | 'tags') => {\n          return typeof init?.next?.[field] !== 'undefined'\n            ? init?.next?.[field]\n            : isRequestInput\n              ? (input as any).next?.[field]\n              : undefined\n        }\n        // RequestInit doesn't keep extra fields e.g. next so it's\n        // only available if init is used separate\n        const originalFetchRevalidate = getNextField('revalidate')\n        let currentFetchRevalidate = originalFetchRevalidate\n        const tags: string[] = validateTags(\n          getNextField('tags') || [],\n          `fetch ${input.toString()}`\n        )\n\n        const revalidateStore =\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            // TODO: stop accumulating tags in client prerender\n            workUnitStore.type === 'prerender-client' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n            ? workUnitStore\n            : undefined\n\n        if (revalidateStore) {\n          if (Array.isArray(tags)) {\n            // Collect tags onto parent caches or parent prerenders.\n            const collectedTags =\n              revalidateStore.tags ?? (revalidateStore.tags = [])\n            for (const tag of tags) {\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const implicitTags = workUnitStore?.implicitTags\n\n        // Inside unstable-cache we treat it the same as force-no-store on the\n        // page.\n        const pageFetchCacheMode =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n            ? 'force-no-store'\n            : workStore.fetchCache\n\n        const isUsingNoStore = !!workStore.isUnstableNoStore\n\n        let currentFetchCacheConfig = getRequestMeta('cache')\n        let cacheReason = ''\n        let cacheWarning: string | undefined\n\n        if (\n          typeof currentFetchCacheConfig === 'string' &&\n          typeof currentFetchRevalidate !== 'undefined'\n        ) {\n          // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n          const isConflictingRevalidate =\n            // revalidate: 0 and cache: force-cache\n            (currentFetchCacheConfig === 'force-cache' &&\n              currentFetchRevalidate === 0) ||\n            // revalidate: >0 or revalidate: false and cache: no-store\n            (currentFetchCacheConfig === 'no-store' &&\n              (currentFetchRevalidate > 0 || currentFetchRevalidate === false))\n\n          if (isConflictingRevalidate) {\n            cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`\n            currentFetchCacheConfig = undefined\n            currentFetchRevalidate = undefined\n          }\n        }\n\n        const hasExplicitFetchCacheOptOut =\n          // fetch config itself signals not to cache\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store' ||\n          // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n          // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n          pageFetchCacheMode === 'force-no-store' ||\n          pageFetchCacheMode === 'only-no-store'\n\n        // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n        // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n        // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n        // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n        // other configurations.\n        const noFetchConfigAndForceDynamic =\n          !pageFetchCacheMode &&\n          !currentFetchCacheConfig &&\n          !currentFetchRevalidate &&\n          workStore.forceDynamic\n\n        if (\n          // force-cache was specified without a revalidate value. We set the revalidate value to false\n          // which will signal the cache to not revalidate\n          currentFetchCacheConfig === 'force-cache' &&\n          typeof currentFetchRevalidate === 'undefined'\n        ) {\n          currentFetchRevalidate = false\n        } else if (\n          hasExplicitFetchCacheOptOut ||\n          noFetchConfigAndForceDynamic\n        ) {\n          currentFetchRevalidate = 0\n        }\n\n        if (\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store'\n        ) {\n          cacheReason = `cache: ${currentFetchCacheConfig}`\n        }\n\n        finalRevalidate = validateRevalidate(\n          currentFetchRevalidate,\n          workStore.route\n        )\n\n        const _headers = getRequestMeta('headers')\n        const initHeaders: Headers =\n          typeof _headers?.get === 'function'\n            ? _headers\n            : new Headers(_headers || {})\n\n        const hasUnCacheableHeader =\n          initHeaders.get('authorization') || initHeaders.get('cookie')\n\n        const isUnCacheableMethod = !['get', 'head'].includes(\n          getRequestMeta('method')?.toLowerCase() || 'get'\n        )\n\n        /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */\n        const hasNoExplicitCacheConfig =\n          // eslint-disable-next-line eqeqeq\n          pageFetchCacheMode == undefined &&\n          // eslint-disable-next-line eqeqeq\n          (currentFetchCacheConfig == undefined ||\n            // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') &&\n          // eslint-disable-next-line eqeqeq\n          currentFetchRevalidate == undefined\n\n        let autoNoCache = Boolean(\n          (hasUnCacheableHeader || isUnCacheableMethod) &&\n            revalidateStore?.revalidate === 0\n        )\n\n        let isImplicitBuildTimeCache = false\n\n        if (!autoNoCache && hasNoExplicitCacheConfig) {\n          // We don't enable automatic no-cache behavior during build-time\n          // prerendering so that we can still leverage the fetch cache between\n          // export workers.\n          if (workStore.isBuildTimePrerendering) {\n            isImplicitBuildTimeCache = true\n          } else {\n            autoNoCache = true\n          }\n        }\n\n        if (\n          hasNoExplicitCacheConfig &&\n          workUnitStore !== undefined &&\n          (workUnitStore.type === 'prerender' ||\n            // While we don't want to do caching in the client scope\n            // we know the fetch will be dynamic for dynamicIO so we\n            // may as well avoid the call here\n            workUnitStore.type === 'prerender-client')\n        ) {\n          // If we have no cache config, and we're in Dynamic I/O prerendering, it'll be a dynamic call.\n          // We don't have to issue that dynamic call.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n            cacheSignal = null\n          }\n          return makeHangingPromise<Response>(\n            workUnitStore.renderSignal,\n            'fetch()'\n          )\n        }\n\n        switch (pageFetchCacheMode) {\n          case 'force-no-store': {\n            cacheReason = 'fetchCache = force-no-store'\n            break\n          }\n          case 'only-no-store': {\n            if (\n              currentFetchCacheConfig === 'force-cache' ||\n              (typeof finalRevalidate !== 'undefined' && finalRevalidate > 0)\n            ) {\n              throw new Error(\n                `cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`\n              )\n            }\n            cacheReason = 'fetchCache = only-no-store'\n            break\n          }\n          case 'only-cache': {\n            if (currentFetchCacheConfig === 'no-store') {\n              throw new Error(\n                `cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`\n              )\n            }\n            break\n          }\n          case 'force-cache': {\n            if (\n              typeof currentFetchRevalidate === 'undefined' ||\n              currentFetchRevalidate === 0\n            ) {\n              cacheReason = 'fetchCache = force-cache'\n              finalRevalidate = INFINITE_CACHE\n            }\n            break\n          }\n          default:\n          // sometimes we won't match the above cases. the reason we don't move\n          // everything to this switch is the use of autoNoCache which is not a fetchCacheMode\n          // I suspect this could be unified with fetchCacheMode however in which case we could\n          // simplify the switch case and ensure we have an exhaustive switch handling all modes\n        }\n\n        if (typeof finalRevalidate === 'undefined') {\n          if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n            finalRevalidate = INFINITE_CACHE\n            cacheReason = 'fetchCache = default-cache'\n          } else if (pageFetchCacheMode === 'default-no-store') {\n            finalRevalidate = 0\n            cacheReason = 'fetchCache = default-no-store'\n          } else if (isUsingNoStore) {\n            finalRevalidate = 0\n            cacheReason = 'noStore call'\n          } else if (autoNoCache) {\n            finalRevalidate = 0\n            cacheReason = 'auto no cache'\n          } else {\n            // TODO: should we consider this case an invariant?\n            cacheReason = 'auto cache'\n            finalRevalidate = revalidateStore\n              ? revalidateStore.revalidate\n              : INFINITE_CACHE\n          }\n        } else if (!cacheReason) {\n          cacheReason = `revalidate: ${finalRevalidate}`\n        }\n\n        if (\n          // when force static is configured we don't bail from\n          // `revalidate: 0` values\n          !(workStore.forceStatic && finalRevalidate === 0) &&\n          // we don't consider autoNoCache to switch to dynamic for ISR\n          !autoNoCache &&\n          // If the revalidate value isn't currently set or the value is less\n          // than the current revalidate value, we should update the revalidate\n          // value.\n          revalidateStore &&\n          finalRevalidate < revalidateStore.revalidate\n        ) {\n          // If we were setting the revalidate value to 0, we should try to\n          // postpone instead first.\n          if (finalRevalidate === 0) {\n            if (workUnitStore) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                case 'prerender-client':\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                    cacheSignal = null\n                  }\n                  return makeHangingPromise<Response>(\n                    workUnitStore.renderSignal,\n                    'fetch()'\n                  )\n                default:\n                // fallthrough\n              }\n            }\n\n            markCurrentScopeAsDynamic(\n              workStore,\n              workUnitStore,\n              `revalidate: 0 fetch ${input} ${workStore.route}`\n            )\n          }\n\n          // We only want to set the revalidate store's revalidate time if it\n          // was explicitly set for the fetch call, i.e.\n          // originalFetchRevalidate.\n          if (revalidateStore && originalFetchRevalidate === finalRevalidate) {\n            revalidateStore.revalidate = finalRevalidate\n          }\n        }\n\n        const isCacheableRevalidate =\n          typeof finalRevalidate === 'number' && finalRevalidate > 0\n\n        let cacheKey: string | undefined\n        const { incrementalCache } = workStore\n\n        const useCacheOrRequestStore =\n          workUnitStore?.type === 'request' || workUnitStore?.type === 'cache'\n            ? workUnitStore\n            : undefined\n\n        if (\n          incrementalCache &&\n          (isCacheableRevalidate ||\n            useCacheOrRequestStore?.serverComponentsHmrCache)\n        ) {\n          try {\n            cacheKey = await incrementalCache.generateCacheKey(\n              fetchUrl,\n              isRequestInput ? (input as RequestInit) : init\n            )\n          } catch (err) {\n            console.error(`Failed to generate cache key for`, input)\n          }\n        }\n\n        const fetchIdx = workStore.nextFetchId ?? 1\n        workStore.nextFetchId = fetchIdx + 1\n\n        let handleUnlock: () => Promise<void> | void = () => {}\n\n        const doOriginalFetch = async (\n          isStale?: boolean,\n          cacheReasonOverride?: string\n        ) => {\n          const requestInputFields = [\n            'cache',\n            'credentials',\n            'headers',\n            'integrity',\n            'keepalive',\n            'method',\n            'mode',\n            'redirect',\n            'referrer',\n            'referrerPolicy',\n            'window',\n            'duplex',\n\n            // don't pass through signal when revalidating\n            ...(isStale ? [] : ['signal']),\n          ]\n\n          if (isRequestInput) {\n            const reqInput: Request = input as any\n            const reqOptions: RequestInit = {\n              body: (reqInput as any)._ogBody || reqInput.body,\n            }\n\n            for (const field of requestInputFields) {\n              // @ts-expect-error custom fields\n              reqOptions[field] = reqInput[field]\n            }\n            input = new Request(reqInput.url, reqOptions)\n          } else if (init) {\n            const { _ogBody, body, signal, ...otherInput } =\n              init as RequestInit & { _ogBody?: any }\n            init = {\n              ...otherInput,\n              body: _ogBody || body,\n              signal: isStale ? undefined : signal,\n            }\n          }\n\n          // add metadata to init without editing the original\n          const clonedInit = {\n            ...init,\n            next: { ...init?.next, fetchType: 'origin', fetchIdx },\n          }\n\n          return originFetch(input, clonedInit)\n            .then(async (res) => {\n              if (!isStale && fetchStart) {\n                trackFetchMetric(workStore, {\n                  start: fetchStart,\n                  url: fetchUrl,\n                  cacheReason: cacheReasonOverride || cacheReason,\n                  cacheStatus:\n                    finalRevalidate === 0 || cacheReasonOverride\n                      ? 'skip'\n                      : 'miss',\n                  cacheWarning,\n                  status: res.status,\n                  method: clonedInit.method || 'GET',\n                })\n              }\n              if (\n                res.status === 200 &&\n                incrementalCache &&\n                cacheKey &&\n                (isCacheableRevalidate ||\n                  useCacheOrRequestStore?.serverComponentsHmrCache)\n              ) {\n                const normalizedRevalidate =\n                  finalRevalidate >= INFINITE_CACHE\n                    ? CACHE_ONE_YEAR\n                    : finalRevalidate\n\n                if (\n                  workUnitStore &&\n                  (workUnitStore.type === 'prerender' ||\n                    workUnitStore.type === 'prerender-client')\n                ) {\n                  // We are prerendering at build time or revalidate time with dynamicIO so we need to\n                  // buffer the response so we can guarantee it can be read in a microtask\n                  const bodyBuffer = await res.arrayBuffer()\n\n                  const fetchedData = {\n                    headers: Object.fromEntries(res.headers.entries()),\n                    body: Buffer.from(bodyBuffer).toString('base64'),\n                    status: res.status,\n                    url: res.url,\n                  }\n\n                  // We can skip checking the serverComponentsHmrCache because we aren't in\n                  // dev mode.\n\n                  await incrementalCache.set(\n                    cacheKey,\n                    {\n                      kind: CachedRouteKind.FETCH,\n                      data: fetchedData,\n                      revalidate: normalizedRevalidate,\n                    },\n                    {\n                      fetchCache: true,\n                      fetchUrl,\n                      fetchIdx,\n                      tags,\n                      isImplicitBuildTimeCache,\n                    }\n                  )\n                  await handleUnlock()\n\n                  // We return a new Response to the caller.\n                  return new Response(bodyBuffer, {\n                    headers: res.headers,\n                    status: res.status,\n                    statusText: res.statusText,\n                  })\n                } else {\n                  // We're cloning the response using this utility because there\n                  // exists a bug in the undici library around response cloning.\n                  // See the following pull request for more details:\n                  // https://github.com/vercel/next.js/pull/73274\n\n                  const [cloned1, cloned2] = cloneResponse(res)\n\n                  // We are dynamically rendering including dev mode. We want to return\n                  // the response to the caller as soon as possible because it might stream\n                  // over a very long time.\n                  const cacheSetPromise = cloned1\n                    .arrayBuffer()\n                    .then(async (arrayBuffer) => {\n                      const bodyBuffer = Buffer.from(arrayBuffer)\n\n                      const fetchedData = {\n                        headers: Object.fromEntries(cloned1.headers.entries()),\n                        body: bodyBuffer.toString('base64'),\n                        status: cloned1.status,\n                        url: cloned1.url,\n                      }\n\n                      useCacheOrRequestStore?.serverComponentsHmrCache?.set(\n                        cacheKey,\n                        fetchedData\n                      )\n\n                      if (isCacheableRevalidate) {\n                        await incrementalCache.set(\n                          cacheKey,\n                          {\n                            kind: CachedRouteKind.FETCH,\n                            data: fetchedData,\n                            revalidate: normalizedRevalidate,\n                          },\n                          {\n                            fetchCache: true,\n                            fetchUrl,\n                            fetchIdx,\n                            tags,\n                            isImplicitBuildTimeCache,\n                          }\n                        )\n                      }\n                    })\n                    .catch((error) =>\n                      console.warn(`Failed to set fetch cache`, input, error)\n                    )\n                    .finally(handleUnlock)\n\n                  const pendingRevalidateKey = `cache-set-${cacheKey}`\n                  workStore.pendingRevalidates ??= {}\n                  if (pendingRevalidateKey in workStore.pendingRevalidates) {\n                    // there is already a pending revalidate entry that\n                    // we need to await to avoid race conditions\n                    await workStore.pendingRevalidates[pendingRevalidateKey]\n                  }\n                  workStore.pendingRevalidates[pendingRevalidateKey] =\n                    cacheSetPromise.finally(() => {\n                      // If the pending revalidate is not present in the store, then\n                      // we have nothing to delete.\n                      if (\n                        !workStore.pendingRevalidates?.[pendingRevalidateKey]\n                      ) {\n                        return\n                      }\n\n                      delete workStore.pendingRevalidates[pendingRevalidateKey]\n                    })\n\n                  return cloned2\n                }\n              }\n\n              // we had response that we determined shouldn't be cached so we return it\n              // and don't cache it. This also needs to unlock the cache lock we acquired.\n              await handleUnlock()\n\n              return res\n            })\n            .catch((error) => {\n              handleUnlock()\n              throw error\n            })\n        }\n\n        let cacheReasonOverride\n        let isForegroundRevalidate = false\n        let isHmrRefreshCache = false\n\n        if (cacheKey && incrementalCache) {\n          let cachedFetchData: CachedFetchData | undefined\n\n          if (\n            useCacheOrRequestStore?.isHmrRefresh &&\n            useCacheOrRequestStore.serverComponentsHmrCache\n          ) {\n            cachedFetchData =\n              useCacheOrRequestStore.serverComponentsHmrCache.get(cacheKey)\n\n            isHmrRefreshCache = true\n          }\n\n          if (isCacheableRevalidate && !cachedFetchData) {\n            handleUnlock = await incrementalCache.lock(cacheKey)\n            const entry = workStore.isOnDemandRevalidate\n              ? null\n              : await incrementalCache.get(cacheKey, {\n                  kind: IncrementalCacheKind.FETCH,\n                  revalidate: finalRevalidate,\n                  fetchUrl,\n                  fetchIdx,\n                  tags,\n                  softTags: implicitTags?.tags,\n                })\n\n            if (hasNoExplicitCacheConfig) {\n              // We sometimes use the cache to dedupe fetches that do not specify a cache configuration\n              // In these cases we want to make sure we still exclude them from prerenders if dynamicIO is on\n              // so we introduce an artificial Task boundary here.\n              if (\n                workUnitStore &&\n                (workUnitStore.type === 'prerender' ||\n                  workUnitStore.type === 'prerender-client')\n              ) {\n                await waitAtLeastOneReactRenderTask()\n              }\n            }\n\n            if (entry) {\n              await handleUnlock()\n            } else {\n              // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n              cacheReasonOverride = 'cache-control: no-cache (hard refresh)'\n            }\n\n            if (entry?.value && entry.value.kind === CachedRouteKind.FETCH) {\n              // when stale and is revalidating we wait for fresh data\n              // so the revalidated entry has the updated data\n              if (workStore.isRevalidate && entry.isStale) {\n                isForegroundRevalidate = true\n              } else {\n                if (entry.isStale) {\n                  workStore.pendingRevalidates ??= {}\n                  if (!workStore.pendingRevalidates[cacheKey]) {\n                    const pendingRevalidate = doOriginalFetch(true)\n                      .then(async (response) => ({\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText,\n                      }))\n                      .finally(() => {\n                        workStore.pendingRevalidates ??= {}\n                        delete workStore.pendingRevalidates[cacheKey || '']\n                      })\n\n                    // Attach the empty catch here so we don't get a \"unhandled\n                    // promise rejection\" warning.\n                    pendingRevalidate.catch(console.error)\n\n                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate\n                  }\n                }\n\n                cachedFetchData = entry.value.data\n              }\n            }\n          }\n\n          if (cachedFetchData) {\n            if (fetchStart) {\n              trackFetchMetric(workStore, {\n                start: fetchStart,\n                url: fetchUrl,\n                cacheReason,\n                cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                cacheWarning,\n                status: cachedFetchData.status || 200,\n                method: init?.method || 'GET',\n              })\n            }\n\n            const response = new Response(\n              Buffer.from(cachedFetchData.body, 'base64'),\n              {\n                headers: cachedFetchData.headers,\n                status: cachedFetchData.status,\n              }\n            )\n\n            Object.defineProperty(response, 'url', {\n              value: cachedFetchData.url,\n            })\n\n            return response\n          }\n        }\n\n        if (workStore.isStaticGeneration && init && typeof init === 'object') {\n          const { cache } = init\n\n          // Delete `cache` property as Cloudflare Workers will throw an error\n          if (isEdgeRuntime) delete init.cache\n\n          if (cache === 'no-store') {\n            // If enabled, we should bail out of static generation.\n            if (workUnitStore) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                case 'prerender-client':\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                    cacheSignal = null\n                  }\n                  return makeHangingPromise<Response>(\n                    workUnitStore.renderSignal,\n                    'fetch()'\n                  )\n                default:\n                // fallthrough\n              }\n            }\n            markCurrentScopeAsDynamic(\n              workStore,\n              workUnitStore,\n              `no-store fetch ${input} ${workStore.route}`\n            )\n          }\n\n          const hasNextConfig = 'next' in init\n          const { next = {} } = init\n          if (\n            typeof next.revalidate === 'number' &&\n            revalidateStore &&\n            next.revalidate < revalidateStore.revalidate\n          ) {\n            if (next.revalidate === 0) {\n              // If enabled, we should bail out of static generation.\n              if (workUnitStore) {\n                switch (workUnitStore.type) {\n                  case 'prerender':\n                  case 'prerender-client':\n                    return makeHangingPromise<Response>(\n                      workUnitStore.renderSignal,\n                      'fetch()'\n                    )\n                  default:\n                  // fallthrough\n                }\n              }\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `revalidate: 0 fetch ${input} ${workStore.route}`\n              )\n            }\n\n            if (!workStore.forceStatic || next.revalidate !== 0) {\n              revalidateStore.revalidate = next.revalidate\n            }\n          }\n          if (hasNextConfig) delete init.next\n        }\n\n        // if we are revalidating the whole page via time or on-demand and\n        // the fetch cache entry is stale we should still de-dupe the\n        // origin hit if it's a cache-able entry\n        if (cacheKey && isForegroundRevalidate) {\n          const pendingRevalidateKey = cacheKey\n          workStore.pendingRevalidates ??= {}\n          let pendingRevalidate =\n            workStore.pendingRevalidates[pendingRevalidateKey]\n\n          if (pendingRevalidate) {\n            const revalidatedResult: {\n              body: ArrayBuffer\n              headers: Headers\n              status: number\n              statusText: string\n            } = await pendingRevalidate\n            return new Response(revalidatedResult.body, {\n              headers: revalidatedResult.headers,\n              status: revalidatedResult.status,\n              statusText: revalidatedResult.statusText,\n            })\n          }\n\n          // We used to just resolve the Response and clone it however for\n          // static generation with dynamicIO we need the response to be able to\n          // be resolved in a microtask and cloning the response will never have\n          // a body that can resolve in a microtask in node (as observed through\n          // experimentation) So instead we await the body and then when it is\n          // available we construct manually cloned Response objects with the\n          // body as an ArrayBuffer. This will be resolvable in a microtask\n          // making it compatible with dynamicIO.\n          const pendingResponse = doOriginalFetch(true, cacheReasonOverride)\n            // We're cloning the response using this utility because there\n            // exists a bug in the undici library around response cloning.\n            // See the following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            .then(cloneResponse)\n\n          pendingRevalidate = pendingResponse\n            .then(async (responses) => {\n              const response = responses[0]\n              return {\n                body: await response.arrayBuffer(),\n                headers: response.headers,\n                status: response.status,\n                statusText: response.statusText,\n              }\n            })\n            .finally(() => {\n              // If the pending revalidate is not present in the store, then\n              // we have nothing to delete.\n              if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n                return\n              }\n\n              delete workStore.pendingRevalidates[pendingRevalidateKey]\n            })\n\n          // Attach the empty catch here so we don't get a \"unhandled promise\n          // rejection\" warning\n          pendingRevalidate.catch(() => {})\n\n          workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate\n\n          return pendingResponse.then((responses) => responses[1])\n        } else {\n          return doOriginalFetch(false, cacheReasonOverride)\n        }\n      }\n    )\n\n    if (cacheSignal) {\n      try {\n        return await result\n      } finally {\n        if (cacheSignal) {\n          cacheSignal.endRead()\n        }\n      }\n    }\n    return result\n  }\n\n  // Attach the necessary properties to the patched fetch function.\n  // We don't use this to determine if the fetch function has been patched,\n  // but for external consumers to determine if the fetch function has been\n  // patched.\n  patched.__nextPatched = true as const\n  patched.__nextGetStaticStore = () => workAsyncStorage\n  patched._nextOriginalFetch = originFetch\n  ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = true\n\n  // Assign the function name also as a name property, so that it's preserved\n  // even when mangling is enabled.\n  Object.defineProperty(patched, 'name', { value: 'fetch', writable: false })\n\n  return patched\n}\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options: PatchableModule) {\n  // If we've already patched fetch, we should not patch it again.\n  if (isFetchPatched()) return\n\n  // Grab the original fetch function. We'll attach this so we can use it in\n  // the patched fetch function.\n  const original = createDedupeFetch(globalThis.fetch)\n\n  // Set the global fetch to the patched fetch.\n  globalThis.fetch = createPatchedFetcher(original, options)\n}\n"], "names": ["NEXT_PATCH_SYMBOL", "createPatchedFetcher", "patchFetch", "validateRevalidate", "validateTags", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "Symbol", "for", "isFetchPatched", "globalThis", "revalidateVal", "route", "normalizedRevalidate", "undefined", "INFINITE_CACHE", "isNaN", "Error", "err", "message", "includes", "tags", "description", "validTags", "invalidTags", "i", "length", "tag", "push", "reason", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_CACHE_TAG_MAX_ITEMS", "console", "warn", "slice", "join", "log", "trackFetchMetric", "workStore", "ctx", "requestEndedState", "ended", "isDebugBuild", "NEXT_DEBUG_BUILD", "NEXT_SSG_FETCH_METRICS", "isStaticGeneration", "isDevelopment", "NODE_ENV", "fetchMetrics", "end", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "idx", "nextFetchId", "originFetch", "workAsyncStorage", "workUnitAsyncStorage", "patched", "fetch", "input", "init", "url", "URL", "Request", "username", "password", "fetchUrl", "href", "method", "toUpperCase", "isInternal", "next", "internal", "hideSpan", "NEXT_OTEL_FETCH_DISABLED", "fetchStart", "getStore", "workUnitStore", "cacheSignal", "type", "beginRead", "result", "getTracer", "trace", "NextNodeServerSpan", "internalFetch", "AppRenderSpan", "kind", "SpanKind", "CLIENT", "spanName", "filter", "Boolean", "attributes", "hostname", "port", "getRequestMeta", "isDraftMode", "isRequestInput", "field", "value", "finalRevalidate", "getNextField", "originalFetchRevalidate", "currentFetchRevalidate", "toString", "revalidateStore", "Array", "isArray", "collectedTags", "implicitTags", "pageFetchCacheMode", "fetchCache", "isUsingNoStore", "isUnstableNoStore", "currentFetchCacheConfig", "cacheReason", "cacheWarning", "isConflictingRevalidate", "hasExplicitFetchCacheOptOut", "noFetchConfigAndForceDynamic", "forceDynamic", "_headers", "initHeaders", "get", "Headers", "hasUnCacheableHeader", "isUnCacheableMethod", "toLowerCase", "hasNoExplicitCacheConfig", "autoNoCache", "revalidate", "isImplicitBuildTimeCache", "isBuildTimePrerendering", "endRead", "makeHangingPromise", "renderSignal", "forceStatic", "markCurrentScopeAsDynamic", "isCacheableRevalidate", "cache<PERSON>ey", "incrementalCache", "useCacheOrRequestStore", "serverComponentsHmrCache", "generate<PERSON>ache<PERSON>ey", "error", "fetchIdx", "handleUnlock", "doOriginalFetch", "isStale", "cacheReasonOverride", "requestInputFields", "reqInput", "reqOptions", "body", "_ogBody", "signal", "otherInput", "clonedInit", "fetchType", "then", "res", "start", "cacheStatus", "status", "CACHE_ONE_YEAR", "bodyBuffer", "arrayBuffer", "fetchedData", "headers", "Object", "fromEntries", "entries", "<PERSON><PERSON><PERSON>", "from", "set", "CachedRouteKind", "FETCH", "data", "Response", "statusText", "cloned1", "cloned2", "cloneResponse", "cacheSetPromise", "catch", "finally", "pendingRevalidateKey", "pendingRevalidates", "isForegroundRevalidate", "isHmrRefreshCache", "cachedFetchData", "isHmrRefresh", "lock", "entry", "isOnDemandRevalidate", "IncrementalCacheKind", "softTags", "waitAtLeastOneReactRenderTask", "isRevalidate", "pendingRevalidate", "response", "defineProperty", "cache", "hasNextConfig", "revalidatedResult", "pendingResponse", "responses", "__nextPatched", "__nextGetStaticStore", "_nextOriginalFetch", "writable", "options", "original", "createDedupeFetch"], "mappings": ";;;;;;;;;;;;;;;;;;IAoCaA,iBAAiB;eAAjBA;;IAiHGC,oBAAoB;eAApBA;;IAg5BAC,UAAU;eAAVA;;IA3/BAC,kBAAkB;eAAlBA;;IA8BAC,YAAY;eAAZA;;;2BAnEkC;wBACd;4BAM7B;kCACmC;uCACP;6BAED;+BAM3B;2BACuC;+BAChB;AAE9B,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAU5C,MAAMR,oBAAoBS,OAAOC,GAAG,CAAC;AAE5C,SAASC;IACP,OAAO,AAACC,UAAsC,CAACZ,kBAAkB,KAAK;AACxE;AAEO,SAASG,mBACdU,aAAsB,EACtBC,KAAa;IAEb,IAAI;QACF,IAAIC,uBAA2CC;QAE/C,IAAIH,kBAAkB,OAAO;YAC3BE,uBAAuBE,0BAAc;QACvC,OAAO,IACL,OAAOJ,kBAAkB,YACzB,CAACK,MAAML,kBACPA,gBAAgB,CAAC,GACjB;YACAE,uBAAuBF;QACzB,OAAO,IAAI,OAAOA,kBAAkB,aAAa;YAC/C,MAAM,qBAEL,CAFK,IAAIM,MACR,CAAC,0BAA0B,EAAEN,cAAc,MAAM,EAAEC,MAAM,yCAAyC,CAAC,GAD/F,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAOC;IACT,EAAE,OAAOK,KAAU;QACjB,0EAA0E;QAC1E,IAAIA,eAAeD,SAASC,IAAIC,OAAO,CAACC,QAAQ,CAAC,uBAAuB;YACtE,MAAMF;QACR;QACA,OAAOJ;IACT;AACF;AAEO,SAASZ,aAAamB,IAAW,EAAEC,WAAmB;IAC3D,MAAMC,YAAsB,EAAE;IAC9B,MAAMC,cAGD,EAAE;IAEP,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,KAAKK,MAAM,EAAED,IAAK;QACpC,MAAME,MAAMN,IAAI,CAACI,EAAE;QAEnB,IAAI,OAAOE,QAAQ,UAAU;YAC3BH,YAAYI,IAAI,CAAC;gBAAED;gBAAKE,QAAQ;YAAiC;QACnE,OAAO,IAAIF,IAAID,MAAM,GAAGI,qCAAyB,EAAE;YACjDN,YAAYI,IAAI,CAAC;gBACfD;gBACAE,QAAQ,CAAC,uBAAuB,EAAEC,qCAAyB,EAAE;YAC/D;QACF,OAAO;YACLP,UAAUK,IAAI,CAACD;QACjB;QAEA,IAAIJ,UAAUG,MAAM,GAAGK,oCAAwB,EAAE;YAC/CC,QAAQC,IAAI,CACV,CAAC,oCAAoC,EAAEX,YAAY,eAAe,CAAC,EACnED,KAAKa,KAAK,CAACT,GAAGU,IAAI,CAAC;YAErB;QACF;IACF;IAEA,IAAIX,YAAYE,MAAM,GAAG,GAAG;QAC1BM,QAAQC,IAAI,CAAC,CAAC,gCAAgC,EAAEX,YAAY,EAAE,CAAC;QAE/D,KAAK,MAAM,EAAEK,GAAG,EAAEE,MAAM,EAAE,IAAIL,YAAa;YACzCQ,QAAQI,GAAG,CAAC,CAAC,MAAM,EAAET,IAAI,EAAE,EAAEE,QAAQ;QACvC;IACF;IACA,OAAON;AACT;AAEA,SAASc,iBACPC,SAAoB,EACpBC,GAAqC;QAIjCD;IAFJ,4EAA4E;IAC5E,IAAI,CAACA,WAAW;IAChB,KAAIA,+BAAAA,UAAUE,iBAAiB,qBAA3BF,6BAA6BG,KAAK,EAAE;IAExC,MAAMC,eACJ,AAAC,CAAA,CAAC,CAACtC,QAAQC,GAAG,CAACsC,gBAAgB,IAC7BvC,QAAQC,GAAG,CAACuC,sBAAsB,KAAK,GAAE,KAC3CN,UAAUO,kBAAkB;IAC9B,MAAMC,gBAAgB1C,QAAQC,GAAG,CAAC0C,QAAQ,KAAK;IAE/C,IACE,8EAA8E;IAC9E,gEAAgE;IAChE,CAACL,gBACD,CAACI,eACD;QACA;IACF;IAEAR,UAAUU,YAAY,KAAK,EAAE;IAE7BV,UAAUU,YAAY,CAACpB,IAAI,CAAC;QAC1B,GAAGW,GAAG;QACNU,KAAKC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAC7CC,KAAKf,UAAUgB,WAAW,IAAI;IAChC;AACF;AAOO,SAASvD,qBACdwD,WAAoB,EACpB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAmB;IAE3D,qCAAqC;IACrC,MAAMC,UAAU,eAAeC,MAC7BC,KAAwB,EACxBC,IAA6B;YAYdA,cAIKA;QAdpB,IAAIC;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAIH,iBAAiBI,UAAUJ,MAAME,GAAG,GAAGF;YACrDE,IAAIG,QAAQ,GAAG;YACfH,IAAII,QAAQ,GAAG;QACjB,EAAE,OAAM;YACN,kEAAkE;YAClEJ,MAAMhD;QACR;QACA,MAAMqD,WAAWL,CAAAA,uBAAAA,IAAKM,IAAI,KAAI;QAC9B,MAAMC,SAASR,CAAAA,yBAAAA,eAAAA,KAAMQ,MAAM,qBAAZR,aAAcS,WAAW,OAAM;QAE9C,yDAAyD;QACzD,oBAAoB;QACpB,MAAMC,aAAa,CAACV,yBAAAA,aAAAA,KAAMW,IAAI,qBAAX,AAACX,WAAoBY,QAAQ,MAAK;QACrD,MAAMC,WAAWtE,QAAQC,GAAG,CAACsE,wBAAwB,KAAK;QAC1D,oDAAoD;QACpD,2EAA2E;QAC3E,mEAAmE;QACnE,6DAA6D;QAC7D,MAAMC,aAAiCL,aACnCzD,YACAoC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAE5C,MAAMd,YAAYkB,iBAAiBqB,QAAQ;QAC3C,MAAMC,gBAAgBrB,qBAAqBoB,QAAQ;QAEnD,sFAAsF;QACtF,IAAIE,cACFD,iBAAiBA,cAAcE,IAAI,KAAK,cACpCF,cAAcC,WAAW,GACzB;QACN,IAAIA,aAAa;YACfA,YAAYE,SAAS;QACvB;QAEA,MAAMC,SAASC,IAAAA,iBAAS,IAAGC,KAAK,CAC9Bb,aAAac,6BAAkB,CAACC,aAAa,GAAGC,wBAAa,CAAC5B,KAAK,EACnE;YACEe;YACAc,MAAMC,gBAAQ,CAACC,MAAM;YACrBC,UAAU;gBAAC;gBAAStB;gBAAQF;aAAS,CAACyB,MAAM,CAACC,SAAS1D,IAAI,CAAC;YAC3D2D,YAAY;gBACV,YAAY3B;gBACZ,eAAeE;gBACf,eAAe,EAAEP,uBAAAA,IAAKiC,QAAQ;gBAC9B,iBAAiBjC,CAAAA,uBAAAA,IAAKkC,IAAI,KAAIlF;YAChC;QACF,GACA;gBAkKImF;YAjKF,wEAAwE;YACxE,IAAI1B,YAAY;gBACd,OAAOhB,YAAYK,OAAOC;YAC5B;YAEA,qDAAqD;YACrD,iEAAiE;YACjE,wBAAwB;YACxB,IAAI,CAACvB,WAAW;gBACd,OAAOiB,YAAYK,OAAOC;YAC5B;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,IAAIvB,UAAU4D,WAAW,EAAE;gBACzB,OAAO3C,YAAYK,OAAOC;YAC5B;YAEA,MAAMsC,iBACJvC,SACA,OAAOA,UAAU,YACjB,OAAO,AAACA,MAAkBS,MAAM,KAAK;YAEvC,MAAM4B,iBAAiB,CAACG;gBACtB,0EAA0E;gBAC1E,MAAMC,QAASxC,wBAAD,AAACA,IAAc,CAACuC,MAAM;gBACpC,OAAOC,SAAUF,CAAAA,iBAAiB,AAACvC,KAAa,CAACwC,MAAM,GAAG,IAAG;YAC/D;YAEA,IAAIE,kBAAsCxF;YAC1C,MAAMyF,eAAe,CAACH;oBACNvC,YACVA,aAEE;gBAHN,OAAO,QAAOA,yBAAAA,aAAAA,KAAMW,IAAI,qBAAVX,UAAY,CAACuC,MAAM,MAAK,cAClCvC,yBAAAA,cAAAA,KAAMW,IAAI,qBAAVX,WAAY,CAACuC,MAAM,GACnBD,kBACE,cAAA,AAACvC,MAAcY,IAAI,qBAAnB,WAAqB,CAAC4B,MAAM,GAC5BtF;YACR;YACA,0DAA0D;YAC1D,0CAA0C;YAC1C,MAAM0F,0BAA0BD,aAAa;YAC7C,IAAIE,yBAAyBD;YAC7B,MAAMnF,OAAiBnB,aACrBqG,aAAa,WAAW,EAAE,EAC1B,CAAC,MAAM,EAAE3C,MAAM8C,QAAQ,IAAI;YAG7B,MAAMC,kBACJ7B,iBACCA,CAAAA,cAAcE,IAAI,KAAK,WACtBF,cAAcE,IAAI,KAAK,eACvB,mDAAmD;YACnDF,cAAcE,IAAI,KAAK,sBACvBF,cAAcE,IAAI,KAAK,mBACvBF,cAAcE,IAAI,KAAK,kBAAiB,IACtCF,gBACAhE;YAEN,IAAI6F,iBAAiB;gBACnB,IAAIC,MAAMC,OAAO,CAACxF,OAAO;oBACvB,wDAAwD;oBACxD,MAAMyF,gBACJH,gBAAgBtF,IAAI,IAAKsF,CAAAA,gBAAgBtF,IAAI,GAAG,EAAE,AAAD;oBACnD,KAAK,MAAMM,OAAON,KAAM;wBACtB,IAAI,CAACyF,cAAc1F,QAAQ,CAACO,MAAM;4BAChCmF,cAAclF,IAAI,CAACD;wBACrB;oBACF;gBACF;YACF;YAEA,MAAMoF,eAAejC,iCAAAA,cAAeiC,YAAY;YAEhD,sEAAsE;YACtE,QAAQ;YACR,MAAMC,qBACJlC,iBAAiBA,cAAcE,IAAI,KAAK,mBACpC,mBACA1C,UAAU2E,UAAU;YAE1B,MAAMC,iBAAiB,CAAC,CAAC5E,UAAU6E,iBAAiB;YAEpD,IAAIC,0BAA0BnB,eAAe;YAC7C,IAAIoB,cAAc;YAClB,IAAIC;YAEJ,IACE,OAAOF,4BAA4B,YACnC,OAAOX,2BAA2B,aAClC;gBACA,oHAAoH;gBACpH,MAAMc,0BAEJ,AADA,uCAAuC;gBACtCH,4BAA4B,iBAC3BX,2BAA2B,KAC7B,0DAA0D;gBACzDW,4BAA4B,cAC1BX,CAAAA,yBAAyB,KAAKA,2BAA2B,KAAI;gBAElE,IAAIc,yBAAyB;oBAC3BD,eAAe,CAAC,kBAAkB,EAAEF,wBAAwB,mBAAmB,EAAEX,uBAAuB,gCAAgC,CAAC;oBACzIW,0BAA0BtG;oBAC1B2F,yBAAyB3F;gBAC3B;YACF;YAEA,MAAM0G,8BACJ,2CAA2C;YAC3CJ,4BAA4B,cAC5BA,4BAA4B,cAC5B,6FAA6F;YAC7F,gFAAgF;YAChFJ,uBAAuB,oBACvBA,uBAAuB;YAEzB,gFAAgF;YAChF,+EAA+E;YAC/E,sFAAsF;YACtF,wFAAwF;YACxF,wBAAwB;YACxB,MAAMS,+BACJ,CAACT,sBACD,CAACI,2BACD,CAACX,0BACDnE,UAAUoF,YAAY;YAExB,IACE,6FAA6F;YAC7F,gDAAgD;YAChDN,4BAA4B,iBAC5B,OAAOX,2BAA2B,aAClC;gBACAA,yBAAyB;YAC3B,OAAO,IACLe,+BACAC,8BACA;gBACAhB,yBAAyB;YAC3B;YAEA,IACEW,4BAA4B,cAC5BA,4BAA4B,YAC5B;gBACAC,cAAc,CAAC,OAAO,EAAED,yBAAyB;YACnD;YAEAd,kBAAkBrG,mBAChBwG,wBACAnE,UAAU1B,KAAK;YAGjB,MAAM+G,WAAW1B,eAAe;YAChC,MAAM2B,cACJ,QAAOD,4BAAAA,SAAUE,GAAG,MAAK,aACrBF,WACA,IAAIG,QAAQH,YAAY,CAAC;YAE/B,MAAMI,uBACJH,YAAYC,GAAG,CAAC,oBAAoBD,YAAYC,GAAG,CAAC;YAEtD,MAAMG,sBAAsB,CAAC;gBAAC;gBAAO;aAAO,CAAC5G,QAAQ,CACnD6E,EAAAA,kBAAAA,eAAe,8BAAfA,gBAA0BgC,WAAW,OAAM;YAG7C;;;;;;;;;SASC,GACD,MAAMC,2BACJ,kCAAkC;YAClClB,sBAAsBlG,aACtB,kCAAkC;YACjCsG,CAAAA,2BAA2BtG,aAC1B,+EAA+E;YAC/E,yEAAyE;YACzEsG,4BAA4B,SAAQ,KACtC,kCAAkC;YAClCX,0BAA0B3F;YAE5B,IAAIqH,cAActC,QAChB,AAACkC,CAAAA,wBAAwBC,mBAAkB,KACzCrB,CAAAA,mCAAAA,gBAAiByB,UAAU,MAAK;YAGpC,IAAIC,2BAA2B;YAE/B,IAAI,CAACF,eAAeD,0BAA0B;gBAC5C,gEAAgE;gBAChE,qEAAqE;gBACrE,kBAAkB;gBAClB,IAAI5F,UAAUgG,uBAAuB,EAAE;oBACrCD,2BAA2B;gBAC7B,OAAO;oBACLF,cAAc;gBAChB;YACF;YAEA,IACED,4BACApD,kBAAkBhE,aACjBgE,CAAAA,cAAcE,IAAI,KAAK,eACtB,wDAAwD;YACxD,wDAAwD;YACxD,kCAAkC;YAClCF,cAAcE,IAAI,KAAK,kBAAiB,GAC1C;gBACA,8FAA8F;gBAC9F,4CAA4C;gBAC5C,IAAID,aAAa;oBACfA,YAAYwD,OAAO;oBACnBxD,cAAc;gBAChB;gBACA,OAAOyD,IAAAA,yCAAkB,EACvB1D,cAAc2D,YAAY,EAC1B;YAEJ;YAEA,OAAQzB;gBACN,KAAK;oBAAkB;wBACrBK,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAiB;wBACpB,IACED,4BAA4B,iBAC3B,OAAOd,oBAAoB,eAAeA,kBAAkB,GAC7D;4BACA,MAAM,qBAEL,CAFK,IAAIrF,MACR,CAAC,uCAAuC,EAAEkD,SAAS,gDAAgD,CAAC,GADhG,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACAkD,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAc;wBACjB,IAAID,4BAA4B,YAAY;4BAC1C,MAAM,qBAEL,CAFK,IAAInG,MACR,CAAC,oCAAoC,EAAEkD,SAAS,6CAA6C,CAAC,GAD1F,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAe;wBAClB,IACE,OAAOsC,2BAA2B,eAClCA,2BAA2B,GAC3B;4BACAY,cAAc;4BACdf,kBAAkBvF,0BAAc;wBAClC;wBACA;oBACF;gBACA;YAKF;YAEA,IAAI,OAAOuF,oBAAoB,aAAa;gBAC1C,IAAIU,uBAAuB,mBAAmB,CAACE,gBAAgB;oBAC7DZ,kBAAkBvF,0BAAc;oBAChCsG,cAAc;gBAChB,OAAO,IAAIL,uBAAuB,oBAAoB;oBACpDV,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO,IAAIH,gBAAgB;oBACzBZ,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO,IAAIc,aAAa;oBACtB7B,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO;oBACL,mDAAmD;oBACnDA,cAAc;oBACdf,kBAAkBK,kBACdA,gBAAgByB,UAAU,GAC1BrH,0BAAc;gBACpB;YACF,OAAO,IAAI,CAACsG,aAAa;gBACvBA,cAAc,CAAC,YAAY,EAAEf,iBAAiB;YAChD;YAEA,IACE,qDAAqD;YACrD,yBAAyB;YACzB,CAAEhE,CAAAA,UAAUoG,WAAW,IAAIpC,oBAAoB,CAAA,KAC/C,6DAA6D;YAC7D,CAAC6B,eACD,mEAAmE;YACnE,qEAAqE;YACrE,SAAS;YACTxB,mBACAL,kBAAkBK,gBAAgByB,UAAU,EAC5C;gBACA,iEAAiE;gBACjE,0BAA0B;gBAC1B,IAAI9B,oBAAoB,GAAG;oBACzB,IAAIxB,eAAe;wBACjB,OAAQA,cAAcE,IAAI;4BACxB,KAAK;4BACL,KAAK;gCACH,IAAID,aAAa;oCACfA,YAAYwD,OAAO;oCACnBxD,cAAc;gCAChB;gCACA,OAAOyD,IAAAA,yCAAkB,EACvB1D,cAAc2D,YAAY,EAC1B;4BAEJ;wBAEF;oBACF;oBAEAE,IAAAA,2CAAyB,EACvBrG,WACAwC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAEtB,UAAU1B,KAAK,EAAE;gBAErD;gBAEA,mEAAmE;gBACnE,8CAA8C;gBAC9C,2BAA2B;gBAC3B,IAAI+F,mBAAmBH,4BAA4BF,iBAAiB;oBAClEK,gBAAgByB,UAAU,GAAG9B;gBAC/B;YACF;YAEA,MAAMsC,wBACJ,OAAOtC,oBAAoB,YAAYA,kBAAkB;YAE3D,IAAIuC;YACJ,MAAM,EAAEC,gBAAgB,EAAE,GAAGxG;YAE7B,MAAMyG,yBACJjE,CAAAA,iCAAAA,cAAeE,IAAI,MAAK,aAAaF,CAAAA,iCAAAA,cAAeE,IAAI,MAAK,UACzDF,gBACAhE;YAEN,IACEgI,oBACCF,CAAAA,0BACCG,0CAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;gBACA,IAAI;oBACFH,WAAW,MAAMC,iBAAiBG,gBAAgB,CAChD9E,UACAgC,iBAAkBvC,QAAwBC;gBAE9C,EAAE,OAAO3C,KAAK;oBACZc,QAAQkH,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAEtF;gBACpD;YACF;YAEA,MAAMuF,WAAW7G,UAAUgB,WAAW,IAAI;YAC1ChB,UAAUgB,WAAW,GAAG6F,WAAW;YAEnC,IAAIC,eAA2C,KAAO;YAEtD,MAAMC,kBAAkB,OACtBC,SACAC;gBAEA,MAAMC,qBAAqB;oBACzB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,8CAA8C;uBAC1CF,UAAU,EAAE,GAAG;wBAAC;qBAAS;iBAC9B;gBAED,IAAInD,gBAAgB;oBAClB,MAAMsD,WAAoB7F;oBAC1B,MAAM8F,aAA0B;wBAC9BC,MAAM,AAACF,SAAiBG,OAAO,IAAIH,SAASE,IAAI;oBAClD;oBAEA,KAAK,MAAMvD,SAASoD,mBAAoB;wBACtC,iCAAiC;wBACjCE,UAAU,CAACtD,MAAM,GAAGqD,QAAQ,CAACrD,MAAM;oBACrC;oBACAxC,QAAQ,IAAII,QAAQyF,SAAS3F,GAAG,EAAE4F;gBACpC,OAAO,IAAI7F,MAAM;oBACf,MAAM,EAAE+F,OAAO,EAAED,IAAI,EAAEE,MAAM,EAAE,GAAGC,YAAY,GAC5CjG;oBACFA,OAAO;wBACL,GAAGiG,UAAU;wBACbH,MAAMC,WAAWD;wBACjBE,QAAQP,UAAUxI,YAAY+I;oBAChC;gBACF;gBAEA,oDAAoD;gBACpD,MAAME,aAAa;oBACjB,GAAGlG,IAAI;oBACPW,MAAM;2BAAKX,wBAAAA,KAAMW,IAAI,AAAb;wBAAewF,WAAW;wBAAUb;oBAAS;gBACvD;gBAEA,OAAO5F,YAAYK,OAAOmG,YACvBE,IAAI,CAAC,OAAOC;oBACX,IAAI,CAACZ,WAAW1E,YAAY;wBAC1BvC,iBAAiBC,WAAW;4BAC1B6H,OAAOvF;4BACPd,KAAKK;4BACLkD,aAAakC,uBAAuBlC;4BACpC+C,aACE9D,oBAAoB,KAAKiD,sBACrB,SACA;4BACNjC;4BACA+C,QAAQH,IAAIG,MAAM;4BAClBhG,QAAQ0F,WAAW1F,MAAM,IAAI;wBAC/B;oBACF;oBACA,IACE6F,IAAIG,MAAM,KAAK,OACfvB,oBACAD,YACCD,CAAAA,0BACCG,0CAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;wBACA,MAAMnI,uBACJyF,mBAAmBvF,0BAAc,GAC7BuJ,0BAAc,GACdhE;wBAEN,IACExB,iBACCA,CAAAA,cAAcE,IAAI,KAAK,eACtBF,cAAcE,IAAI,KAAK,kBAAiB,GAC1C;4BACA,oFAAoF;4BACpF,wEAAwE;4BACxE,MAAMuF,aAAa,MAAML,IAAIM,WAAW;4BAExC,MAAMC,cAAc;gCAClBC,SAASC,OAAOC,WAAW,CAACV,IAAIQ,OAAO,CAACG,OAAO;gCAC/ClB,MAAMmB,OAAOC,IAAI,CAACR,YAAY7D,QAAQ,CAAC;gCACvC2D,QAAQH,IAAIG,MAAM;gCAClBvG,KAAKoG,IAAIpG,GAAG;4BACd;4BAEA,yEAAyE;4BACzE,YAAY;4BAEZ,MAAMgF,iBAAiBkC,GAAG,CACxBnC,UACA;gCACErD,MAAMyF,8BAAe,CAACC,KAAK;gCAC3BC,MAAMV;gCACNrC,YAAYvH;4BACd,GACA;gCACEoG,YAAY;gCACZ9C;gCACAgF;gCACA9H;gCACAgH;4BACF;4BAEF,MAAMe;4BAEN,0CAA0C;4BAC1C,OAAO,IAAIgC,SAASb,YAAY;gCAC9BG,SAASR,IAAIQ,OAAO;gCACpBL,QAAQH,IAAIG,MAAM;gCAClBgB,YAAYnB,IAAImB,UAAU;4BAC5B;wBACF,OAAO;4BACL,8DAA8D;4BAC9D,8DAA8D;4BAC9D,mDAAmD;4BACnD,+CAA+C;4BAE/C,MAAM,CAACC,SAASC,QAAQ,GAAGC,IAAAA,4BAAa,EAACtB;4BAEzC,qEAAqE;4BACrE,yEAAyE;4BACzE,yBAAyB;4BACzB,MAAMuB,kBAAkBH,QACrBd,WAAW,GACXP,IAAI,CAAC,OAAOO;oCAUXzB;gCATA,MAAMwB,aAAaO,OAAOC,IAAI,CAACP;gCAE/B,MAAMC,cAAc;oCAClBC,SAASC,OAAOC,WAAW,CAACU,QAAQZ,OAAO,CAACG,OAAO;oCACnDlB,MAAMY,WAAW7D,QAAQ,CAAC;oCAC1B2D,QAAQiB,QAAQjB,MAAM;oCACtBvG,KAAKwH,QAAQxH,GAAG;gCAClB;gCAEAiF,2CAAAA,mDAAAA,uBAAwBC,wBAAwB,qBAAhDD,iDAAkDiC,GAAG,CACnDnC,UACA4B;gCAGF,IAAI7B,uBAAuB;oCACzB,MAAME,iBAAiBkC,GAAG,CACxBnC,UACA;wCACErD,MAAMyF,8BAAe,CAACC,KAAK;wCAC3BC,MAAMV;wCACNrC,YAAYvH;oCACd,GACA;wCACEoG,YAAY;wCACZ9C;wCACAgF;wCACA9H;wCACAgH;oCACF;gCAEJ;4BACF,GACCqD,KAAK,CAAC,CAACxC,QACNlH,QAAQC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE2B,OAAOsF,QAElDyC,OAAO,CAACvC;4BAEX,MAAMwC,uBAAuB,CAAC,UAAU,EAAE/C,UAAU;4BACpDvG,UAAUuJ,kBAAkB,KAAK,CAAC;4BAClC,IAAID,wBAAwBtJ,UAAUuJ,kBAAkB,EAAE;gCACxD,mDAAmD;gCACnD,4CAA4C;gCAC5C,MAAMvJ,UAAUuJ,kBAAkB,CAACD,qBAAqB;4BAC1D;4BACAtJ,UAAUuJ,kBAAkB,CAACD,qBAAqB,GAChDH,gBAAgBE,OAAO,CAAC;oCAInBrJ;gCAHH,8DAA8D;gCAC9D,6BAA6B;gCAC7B,IACE,GAACA,gCAAAA,UAAUuJ,kBAAkB,qBAA5BvJ,6BAA8B,CAACsJ,qBAAqB,GACrD;oCACA;gCACF;gCAEA,OAAOtJ,UAAUuJ,kBAAkB,CAACD,qBAAqB;4BAC3D;4BAEF,OAAOL;wBACT;oBACF;oBAEA,yEAAyE;oBACzE,4EAA4E;oBAC5E,MAAMnC;oBAEN,OAAOc;gBACT,GACCwB,KAAK,CAAC,CAACxC;oBACNE;oBACA,MAAMF;gBACR;YACJ;YAEA,IAAIK;YACJ,IAAIuC,yBAAyB;YAC7B,IAAIC,oBAAoB;YAExB,IAAIlD,YAAYC,kBAAkB;gBAChC,IAAIkD;gBAEJ,IACEjD,CAAAA,0CAAAA,uBAAwBkD,YAAY,KACpClD,uBAAuBC,wBAAwB,EAC/C;oBACAgD,kBACEjD,uBAAuBC,wBAAwB,CAACnB,GAAG,CAACgB;oBAEtDkD,oBAAoB;gBACtB;gBAEA,IAAInD,yBAAyB,CAACoD,iBAAiB;oBAC7C5C,eAAe,MAAMN,iBAAiBoD,IAAI,CAACrD;oBAC3C,MAAMsD,QAAQ7J,UAAU8J,oBAAoB,GACxC,OACA,MAAMtD,iBAAiBjB,GAAG,CAACgB,UAAU;wBACnCrD,MAAM6G,mCAAoB,CAACnB,KAAK;wBAChC9C,YAAY9B;wBACZnC;wBACAgF;wBACA9H;wBACAiL,QAAQ,EAAEvF,gCAAAA,aAAc1F,IAAI;oBAC9B;oBAEJ,IAAI6G,0BAA0B;wBAC5B,yFAAyF;wBACzF,+FAA+F;wBAC/F,oDAAoD;wBACpD,IACEpD,iBACCA,CAAAA,cAAcE,IAAI,KAAK,eACtBF,cAAcE,IAAI,KAAK,kBAAiB,GAC1C;4BACA,MAAMuH,IAAAA,wCAA6B;wBACrC;oBACF;oBAEA,IAAIJ,OAAO;wBACT,MAAM/C;oBACR,OAAO;wBACL,4HAA4H;wBAC5HG,sBAAsB;oBACxB;oBAEA,IAAI4C,CAAAA,yBAAAA,MAAO9F,KAAK,KAAI8F,MAAM9F,KAAK,CAACb,IAAI,KAAKyF,8BAAe,CAACC,KAAK,EAAE;wBAC9D,wDAAwD;wBACxD,gDAAgD;wBAChD,IAAI5I,UAAUkK,YAAY,IAAIL,MAAM7C,OAAO,EAAE;4BAC3CwC,yBAAyB;wBAC3B,OAAO;4BACL,IAAIK,MAAM7C,OAAO,EAAE;gCACjBhH,UAAUuJ,kBAAkB,KAAK,CAAC;gCAClC,IAAI,CAACvJ,UAAUuJ,kBAAkB,CAAChD,SAAS,EAAE;oCAC3C,MAAM4D,oBAAoBpD,gBAAgB,MACvCY,IAAI,CAAC,OAAOyC,WAAc,CAAA;4CACzB/C,MAAM,MAAM+C,SAASlC,WAAW;4CAChCE,SAASgC,SAAShC,OAAO;4CACzBL,QAAQqC,SAASrC,MAAM;4CACvBgB,YAAYqB,SAASrB,UAAU;wCACjC,CAAA,GACCM,OAAO,CAAC;wCACPrJ,UAAUuJ,kBAAkB,KAAK,CAAC;wCAClC,OAAOvJ,UAAUuJ,kBAAkB,CAAChD,YAAY,GAAG;oCACrD;oCAEF,2DAA2D;oCAC3D,8BAA8B;oCAC9B4D,kBAAkBf,KAAK,CAAC1J,QAAQkH,KAAK;oCAErC5G,UAAUuJ,kBAAkB,CAAChD,SAAS,GAAG4D;gCAC3C;4BACF;4BAEAT,kBAAkBG,MAAM9F,KAAK,CAAC8E,IAAI;wBACpC;oBACF;gBACF;gBAEA,IAAIa,iBAAiB;oBACnB,IAAIpH,YAAY;wBACdvC,iBAAiBC,WAAW;4BAC1B6H,OAAOvF;4BACPd,KAAKK;4BACLkD;4BACA+C,aAAa2B,oBAAoB,QAAQ;4BACzCzE;4BACA+C,QAAQ2B,gBAAgB3B,MAAM,IAAI;4BAClChG,QAAQR,CAAAA,wBAAAA,KAAMQ,MAAM,KAAI;wBAC1B;oBACF;oBAEA,MAAMqI,WAAW,IAAItB,SACnBN,OAAOC,IAAI,CAACiB,gBAAgBrC,IAAI,EAAE,WAClC;wBACEe,SAASsB,gBAAgBtB,OAAO;wBAChCL,QAAQ2B,gBAAgB3B,MAAM;oBAChC;oBAGFM,OAAOgC,cAAc,CAACD,UAAU,OAAO;wBACrCrG,OAAO2F,gBAAgBlI,GAAG;oBAC5B;oBAEA,OAAO4I;gBACT;YACF;YAEA,IAAIpK,UAAUO,kBAAkB,IAAIgB,QAAQ,OAAOA,SAAS,UAAU;gBACpE,MAAM,EAAE+I,KAAK,EAAE,GAAG/I;gBAElB,oEAAoE;gBACpE,IAAI1D,eAAe,OAAO0D,KAAK+I,KAAK;gBAEpC,IAAIA,UAAU,YAAY;oBACxB,uDAAuD;oBACvD,IAAI9H,eAAe;wBACjB,OAAQA,cAAcE,IAAI;4BACxB,KAAK;4BACL,KAAK;gCACH,IAAID,aAAa;oCACfA,YAAYwD,OAAO;oCACnBxD,cAAc;gCAChB;gCACA,OAAOyD,IAAAA,yCAAkB,EACvB1D,cAAc2D,YAAY,EAC1B;4BAEJ;wBAEF;oBACF;oBACAE,IAAAA,2CAAyB,EACvBrG,WACAwC,eACA,CAAC,eAAe,EAAElB,MAAM,CAAC,EAAEtB,UAAU1B,KAAK,EAAE;gBAEhD;gBAEA,MAAMiM,gBAAgB,UAAUhJ;gBAChC,MAAM,EAAEW,OAAO,CAAC,CAAC,EAAE,GAAGX;gBACtB,IACE,OAAOW,KAAK4D,UAAU,KAAK,YAC3BzB,mBACAnC,KAAK4D,UAAU,GAAGzB,gBAAgByB,UAAU,EAC5C;oBACA,IAAI5D,KAAK4D,UAAU,KAAK,GAAG;wBACzB,uDAAuD;wBACvD,IAAItD,eAAe;4BACjB,OAAQA,cAAcE,IAAI;gCACxB,KAAK;gCACL,KAAK;oCACH,OAAOwD,IAAAA,yCAAkB,EACvB1D,cAAc2D,YAAY,EAC1B;gCAEJ;4BAEF;wBACF;wBACAE,IAAAA,2CAAyB,EACvBrG,WACAwC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAEtB,UAAU1B,KAAK,EAAE;oBAErD;oBAEA,IAAI,CAAC0B,UAAUoG,WAAW,IAAIlE,KAAK4D,UAAU,KAAK,GAAG;wBACnDzB,gBAAgByB,UAAU,GAAG5D,KAAK4D,UAAU;oBAC9C;gBACF;gBACA,IAAIyE,eAAe,OAAOhJ,KAAKW,IAAI;YACrC;YAEA,kEAAkE;YAClE,6DAA6D;YAC7D,wCAAwC;YACxC,IAAIqE,YAAYiD,wBAAwB;gBACtC,MAAMF,uBAAuB/C;gBAC7BvG,UAAUuJ,kBAAkB,KAAK,CAAC;gBAClC,IAAIY,oBACFnK,UAAUuJ,kBAAkB,CAACD,qBAAqB;gBAEpD,IAAIa,mBAAmB;oBACrB,MAAMK,oBAKF,MAAML;oBACV,OAAO,IAAIrB,SAAS0B,kBAAkBnD,IAAI,EAAE;wBAC1Ce,SAASoC,kBAAkBpC,OAAO;wBAClCL,QAAQyC,kBAAkBzC,MAAM;wBAChCgB,YAAYyB,kBAAkBzB,UAAU;oBAC1C;gBACF;gBAEA,gEAAgE;gBAChE,sEAAsE;gBACtE,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnE,iEAAiE;gBACjE,uCAAuC;gBACvC,MAAM0B,kBAAkB1D,gBAAgB,MAAME,oBAC5C,8DAA8D;gBAC9D,8DAA8D;gBAC9D,mDAAmD;gBACnD,+CAA+C;iBAC9CU,IAAI,CAACuB,4BAAa;gBAErBiB,oBAAoBM,gBACjB9C,IAAI,CAAC,OAAO+C;oBACX,MAAMN,WAAWM,SAAS,CAAC,EAAE;oBAC7B,OAAO;wBACLrD,MAAM,MAAM+C,SAASlC,WAAW;wBAChCE,SAASgC,SAAShC,OAAO;wBACzBL,QAAQqC,SAASrC,MAAM;wBACvBgB,YAAYqB,SAASrB,UAAU;oBACjC;gBACF,GACCM,OAAO,CAAC;wBAGFrJ;oBAFL,8DAA8D;oBAC9D,6BAA6B;oBAC7B,IAAI,GAACA,gCAAAA,UAAUuJ,kBAAkB,qBAA5BvJ,6BAA8B,CAACsJ,qBAAqB,GAAE;wBACzD;oBACF;oBAEA,OAAOtJ,UAAUuJ,kBAAkB,CAACD,qBAAqB;gBAC3D;gBAEF,mEAAmE;gBACnE,qBAAqB;gBACrBa,kBAAkBf,KAAK,CAAC,KAAO;gBAE/BpJ,UAAUuJ,kBAAkB,CAACD,qBAAqB,GAAGa;gBAErD,OAAOM,gBAAgB9C,IAAI,CAAC,CAAC+C,YAAcA,SAAS,CAAC,EAAE;YACzD,OAAO;gBACL,OAAO3D,gBAAgB,OAAOE;YAChC;QACF;QAGF,IAAIxE,aAAa;YACf,IAAI;gBACF,OAAO,MAAMG;YACf,SAAU;gBACR,IAAIH,aAAa;oBACfA,YAAYwD,OAAO;gBACrB;YACF;QACF;QACA,OAAOrD;IACT;IAEA,iEAAiE;IACjE,yEAAyE;IACzE,yEAAyE;IACzE,WAAW;IACXxB,QAAQuJ,aAAa,GAAG;IACxBvJ,QAAQwJ,oBAAoB,GAAG,IAAM1J;IACrCE,QAAQyJ,kBAAkB,GAAG5J;IAC3B7C,UAAsC,CAACZ,kBAAkB,GAAG;IAE9D,2EAA2E;IAC3E,iCAAiC;IACjC6K,OAAOgC,cAAc,CAACjJ,SAAS,QAAQ;QAAE2C,OAAO;QAAS+G,UAAU;IAAM;IAEzE,OAAO1J;AACT;AAGO,SAAS1D,WAAWqN,OAAwB;IACjD,gEAAgE;IAChE,IAAI5M,kBAAkB;IAEtB,0EAA0E;IAC1E,8BAA8B;IAC9B,MAAM6M,WAAWC,IAAAA,8BAAiB,EAAC7M,WAAWiD,KAAK;IAEnD,6CAA6C;IAC7CjD,WAAWiD,KAAK,GAAG5D,qBAAqBuN,UAAUD;AACpD", "ignoreList": [0]}
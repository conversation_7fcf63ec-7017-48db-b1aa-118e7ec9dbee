{"version": 3, "sources": ["../../src/client/route-announcer.tsx"], "sourcesContent": ["import React from 'react'\nimport { useRouter } from './router'\n\nconst nextjsRouteAnnouncerStyles: React.CSSProperties = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  top: 0,\n  width: '1px',\n\n  // https://medium.com/@jessebeach/beware-smushed-off-screen-accessible-text-5952a4c2cbfe\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}\n\nexport const RouteAnnouncer = () => {\n  const { asPath } = useRouter()\n  const [routeAnnouncement, setRouteAnnouncement] = React.useState('')\n\n  // Only announce the path change, but not for the first load because screen\n  // reader will do that automatically.\n  const previouslyLoadedPath = React.useRef(asPath)\n\n  // Every time the path changes, announce the new page’s title following this\n  // priority: first the document title (from head), otherwise the first h1, or\n  // if none of these exist, then the pathname from the URL. This methodology is\n  // inspired by <PERSON><PERSON>’s accessible client routing user testing. More\n  // information can be found here:\n  // https://www.gatsbyjs.com/blog/2019-07-11-user-testing-accessible-client-routing/\n  React.useEffect(\n    () => {\n      // If the path hasn't change, we do nothing.\n      if (previouslyLoadedPath.current === asPath) return\n      previouslyLoadedPath.current = asPath\n\n      if (document.title) {\n        setRouteAnnouncement(document.title)\n      } else {\n        const pageHeader = document.querySelector('h1')\n        const content = pageHeader?.innerText ?? pageHeader?.textContent\n\n        setRouteAnnouncement(content || asPath)\n      }\n    },\n    // TODO: switch to pathname + query object of dynamic route requirements\n    [asPath]\n  )\n\n  return (\n    <p\n      aria-live=\"assertive\" // Make the announcement immediately.\n      id=\"__next-route-announcer__\"\n      role=\"alert\"\n      style={nextjsRouteAnnouncerStyles}\n    >\n      {routeAnnouncement}\n    </p>\n  )\n}\n\nexport default RouteAnnouncer\n"], "names": ["React", "useRouter", "nextjsRouteAnnouncerStyles", "border", "clip", "height", "margin", "overflow", "padding", "position", "top", "width", "whiteSpace", "wordWrap", "RouteAnnouncer", "<PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "useState", "previouslyLoaded<PERSON><PERSON>", "useRef", "useEffect", "current", "document", "title", "pageHeader", "querySelector", "content", "innerText", "textContent", "p", "aria-live", "id", "role", "style"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,SAAS,QAAQ,WAAU;AAEpC,MAAMC,6BAAkD;IACtDC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,KAAK;IACLC,OAAO;IAEP,wFAAwF;IACxFC,YAAY;IACZC,UAAU;AACZ;AAEA,OAAO,MAAMC,iBAAiB;IAC5B,MAAM,EAAEC,MAAM,EAAE,GAAGd;IACnB,MAAM,CAACe,mBAAmBC,qBAAqB,GAAGjB,MAAMkB,QAAQ,CAAC;IAEjE,2EAA2E;IAC3E,qCAAqC;IACrC,MAAMC,uBAAuBnB,MAAMoB,MAAM,CAACL;IAE1C,4EAA4E;IAC5E,6EAA6E;IAC7E,8EAA8E;IAC9E,0EAA0E;IAC1E,iCAAiC;IACjC,mFAAmF;IACnFf,MAAMqB,SAAS,CACb;QACE,4CAA4C;QAC5C,IAAIF,qBAAqBG,OAAO,KAAKP,QAAQ;QAC7CI,qBAAqBG,OAAO,GAAGP;QAE/B,IAAIQ,SAASC,KAAK,EAAE;YAClBP,qBAAqBM,SAASC,KAAK;QACrC,OAAO;YACL,MAAMC,aAAaF,SAASG,aAAa,CAAC;gBAC1BD;YAAhB,MAAME,UAAUF,CAAAA,wBAAAA,8BAAAA,WAAYG,SAAS,YAArBH,wBAAyBA,8BAAAA,WAAYI,WAAW;YAEhEZ,qBAAqBU,WAAWZ;QAClC;IACF,GACA,wEAAwE;IACxE;QAACA;KAAO;IAGV,qBACE,KAACe;QACCC,aAAU,YAAY,qCAAqC;;QAC3DC,IAAG;QACHC,MAAK;QACLC,OAAOhC;kBAENc;;AAGP,EAAC;AAED,eAAeF,eAAc", "ignoreList": [0]}
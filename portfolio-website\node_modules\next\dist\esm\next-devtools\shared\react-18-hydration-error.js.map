{"version": 3, "sources": ["../../../src/next-devtools/shared/react-18-hydration-error.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\n\nexport function isHydrationError(error: unknown): boolean {\n  return (\n    isError(error) &&\n    (error.message ===\n      'Hydration failed because the initial UI does not match what was rendered on the server.' ||\n      error.message === 'Text content does not match server-rendered HTML.')\n  )\n}\n\nexport function isHydrationWarning(message: unknown): message is string {\n  return (\n    isHtmlTagsWarning(message) ||\n    isTextInTagsMismatchWarning(message) ||\n    isTextWarning(message)\n  )\n}\n\ntype NullableText = string | null | undefined\n\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n  'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n  'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s',\n])\nconst textAndTagsMismatchWarnings = new Set([\n  'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n  'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s',\n])\nconst textWarnings = new Set([\n  'Warning: Text content did not match. Server: \"%s\" Client: \"%s\"%s',\n])\n\nexport const getHydrationWarningType = (\n  message: NullableText\n): 'tag' | 'text' | 'text-in-tag' => {\n  if (typeof message !== 'string') {\n    // TODO: Doesn't make sense to treat no message as a hydration error message.\n    // We should bail out somewhere earlier.\n    return 'text'\n  }\n\n  const normalizedMessage = message.startsWith('Warning: ')\n    ? message\n    : `Warning: ${message}`\n\n  if (isHtmlTagsWarning(normalizedMessage)) return 'tag'\n  if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag'\n\n  return 'text'\n}\n\nconst isHtmlTagsWarning = (message: unknown) =>\n  typeof message === 'string' && htmlTagsWarnings.has(message)\n\nconst isTextInTagsMismatchWarning = (msg: unknown) =>\n  typeof msg === 'string' && textAndTagsMismatchWarnings.has(msg)\n\nconst isTextWarning = (msg: unknown) =>\n  typeof msg === 'string' && textWarnings.has(msg)\n"], "names": ["isError", "isHydrationError", "error", "message", "isHydrationWarning", "isHtmlTagsWarning", "isTextInTagsMismatchWarning", "isTextWarning", "htmlTagsWarnings", "Set", "textAndTagsMismatchWarnings", "textWarnings", "getHydrationWarningType", "normalizedMessage", "startsWith", "has", "msg"], "mappings": "AAAA,OAAOA,aAAa,qBAAoB;AAExC,OAAO,SAASC,iBAAiBC,KAAc;IAC7C,OACEF,QAAQE,UACPA,CAAAA,MAAMC,OAAO,KACZ,6FACAD,MAAMC,OAAO,KAAK,mDAAkD;AAE1E;AAEA,OAAO,SAASC,mBAAmBD,OAAgB;IACjD,OACEE,kBAAkBF,YAClBG,4BAA4BH,YAC5BI,cAAcJ;AAElB;AAIA,iIAAiI;AACjI,MAAMK,mBAAmB,IAAIC,IAAI;IAC/B;IACA;CACD;AACD,MAAMC,8BAA8B,IAAID,IAAI;IAC1C;IACA;CACD;AACD,MAAME,eAAe,IAAIF,IAAI;IAC3B;CACD;AAED,OAAO,MAAMG,0BAA0B,CACrCT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,6EAA6E;QAC7E,wCAAwC;QACxC,OAAO;IACT;IAEA,MAAMU,oBAAoBV,QAAQW,UAAU,CAAC,eACzCX,UACA,AAAC,cAAWA;IAEhB,IAAIE,kBAAkBQ,oBAAoB,OAAO;IACjD,IAAIP,4BAA4BO,oBAAoB,OAAO;IAE3D,OAAO;AACT,EAAC;AAED,MAAMR,oBAAoB,CAACF,UACzB,OAAOA,YAAY,YAAYK,iBAAiBO,GAAG,CAACZ;AAEtD,MAAMG,8BAA8B,CAACU,MACnC,OAAOA,QAAQ,YAAYN,4BAA4BK,GAAG,CAACC;AAE7D,MAAMT,gBAAgB,CAACS,MACrB,OAAOA,QAAQ,YAAYL,aAAaI,GAAG,CAACC", "ignoreList": [0]}
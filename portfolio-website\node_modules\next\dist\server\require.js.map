{"version": 3, "sources": ["../../src/server/require.ts"], "sourcesContent": ["import path from 'path'\nimport {\n  PAGES_MANIFEST,\n  SERVER_DIRECTORY,\n  APP_PATHS_MANIFEST,\n} from '../shared/lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport { PageNotFoundError, MissingStaticPage } from '../shared/lib/utils'\nimport { LRUCache } from '../server/lib/lru-cache'\nimport { loadManifest } from './load-manifest.external'\nimport { promises } from 'fs'\n\nconst isDev = process.env.NODE_ENV === 'development'\nconst pagePathCache = !isDev ? new LRUCache<string | null>(1000) : null\n\nexport function getMaybePagePath(\n  page: string,\n  distDir: string,\n  locales: readonly string[] | undefined,\n  isAppPath: boolean\n): string | null {\n  const cacheKey = `${page}:${distDir}:${locales}:${isAppPath}`\n\n  let pagePath = pagePathCache?.get(cacheKey)\n\n  // If we have a cached path, we can return it directly.\n  if (pagePath) return pagePath\n\n  const serverBuildPath = path.join(distDir, SERVER_DIRECTORY)\n  let appPathsManifest: undefined | PagesManifest\n\n  if (isAppPath) {\n    appPathsManifest = loadManifest(\n      path.join(serverBuildPath, APP_PATHS_MANIFEST),\n      !isDev\n    ) as PagesManifest\n  }\n  const pagesManifest = loadManifest(\n    path.join(serverBuildPath, PAGES_MANIFEST),\n    !isDev\n  ) as PagesManifest\n\n  try {\n    page = denormalizePagePath(normalizePagePath(page))\n  } catch (err) {\n    console.error(err)\n    throw new PageNotFoundError(page)\n  }\n\n  const checkManifest = (manifest: PagesManifest) => {\n    let curPath = manifest[page]\n\n    if (!manifest[curPath] && locales) {\n      const manifestNoLocales: typeof pagesManifest = {}\n\n      for (const key of Object.keys(manifest)) {\n        manifestNoLocales[normalizeLocalePath(key, locales).pathname] =\n          pagesManifest[key]\n      }\n      curPath = manifestNoLocales[page]\n    }\n    return curPath\n  }\n\n  if (appPathsManifest) {\n    pagePath = checkManifest(appPathsManifest)\n  }\n\n  if (!pagePath) {\n    pagePath = checkManifest(pagesManifest)\n  }\n\n  if (!pagePath) {\n    pagePathCache?.set(cacheKey, null)\n    return null\n  }\n\n  pagePath = path.join(serverBuildPath, pagePath)\n\n  pagePathCache?.set(cacheKey, pagePath)\n  return pagePath\n}\n\nexport function getPagePath(\n  page: string,\n  distDir: string,\n  locales: string[] | undefined,\n  isAppPath: boolean\n): string {\n  const pagePath = getMaybePagePath(page, distDir, locales, isAppPath)\n\n  if (!pagePath) {\n    throw new PageNotFoundError(page)\n  }\n\n  return pagePath\n}\n\nexport async function requirePage(\n  page: string,\n  distDir: string,\n  isAppPath: boolean\n): Promise<any> {\n  const pagePath = getPagePath(page, distDir, undefined, isAppPath)\n  if (pagePath.endsWith('.html')) {\n    return promises.readFile(pagePath, 'utf8').catch((err) => {\n      throw new MissingStaticPage(page, err.message)\n    })\n  }\n\n  const mod = process.env.NEXT_MINIMAL\n    ? // @ts-ignore\n      __non_webpack_require__(pagePath)\n    : require(pagePath)\n  return mod\n}\n"], "names": ["getMaybePagePath", "getPagePath", "requirePage", "isDev", "process", "env", "NODE_ENV", "pagePath<PERSON>ache", "L<PERSON><PERSON><PERSON>", "page", "distDir", "locales", "isAppPath", "cache<PERSON>ey", "pagePath", "get", "serverBuildPath", "path", "join", "SERVER_DIRECTORY", "appPathsManifest", "loadManifest", "APP_PATHS_MANIFEST", "pagesManifest", "PAGES_MANIFEST", "denormalizePagePath", "normalizePagePath", "err", "console", "error", "PageNotFoundError", "checkManifest", "manifest", "curPath", "manifestNoLocales", "key", "Object", "keys", "normalizeLocalePath", "pathname", "set", "undefined", "endsWith", "promises", "readFile", "catch", "MissingStaticPage", "message", "mod", "NEXT_MINIMAL", "__non_webpack_require__", "require"], "mappings": ";;;;;;;;;;;;;;;;IAkBgBA,gBAAgB;eAAhBA;;IAoEAC,WAAW;eAAXA;;IAeMC,WAAW;eAAXA;;;6DArGL;2BAKV;qCAC6B;mCACF;qCACE;uBAEiB;0BAC5B;sCACI;oBACJ;;;;;;AAEzB,MAAMC,QAAQC,QAAQC,GAAG,CAACC,QAAQ,KAAK;AACvC,MAAMC,gBAAgB,CAACJ,QAAQ,IAAIK,kBAAQ,CAAgB,QAAQ;AAE5D,SAASR,iBACdS,IAAY,EACZC,OAAe,EACfC,OAAsC,EACtCC,SAAkB;IAElB,MAAMC,WAAW,GAAGJ,KAAK,CAAC,EAAEC,QAAQ,CAAC,EAAEC,QAAQ,CAAC,EAAEC,WAAW;IAE7D,IAAIE,WAAWP,iCAAAA,cAAeQ,GAAG,CAACF;IAElC,uDAAuD;IACvD,IAAIC,UAAU,OAAOA;IAErB,MAAME,kBAAkBC,aAAI,CAACC,IAAI,CAACR,SAASS,2BAAgB;IAC3D,IAAIC;IAEJ,IAAIR,WAAW;QACbQ,mBAAmBC,IAAAA,kCAAY,EAC7BJ,aAAI,CAACC,IAAI,CAACF,iBAAiBM,6BAAkB,GAC7C,CAACnB;IAEL;IACA,MAAMoB,gBAAgBF,IAAAA,kCAAY,EAChCJ,aAAI,CAACC,IAAI,CAACF,iBAAiBQ,yBAAc,GACzC,CAACrB;IAGH,IAAI;QACFM,OAAOgB,IAAAA,wCAAmB,EAACC,IAAAA,oCAAiB,EAACjB;IAC/C,EAAE,OAAOkB,KAAK;QACZC,QAAQC,KAAK,CAACF;QACd,MAAM,IAAIG,wBAAiB,CAACrB;IAC9B;IAEA,MAAMsB,gBAAgB,CAACC;QACrB,IAAIC,UAAUD,QAAQ,CAACvB,KAAK;QAE5B,IAAI,CAACuB,QAAQ,CAACC,QAAQ,IAAItB,SAAS;YACjC,MAAMuB,oBAA0C,CAAC;YAEjD,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACL,UAAW;gBACvCE,iBAAiB,CAACI,IAAAA,wCAAmB,EAACH,KAAKxB,SAAS4B,QAAQ,CAAC,GAC3DhB,aAAa,CAACY,IAAI;YACtB;YACAF,UAAUC,iBAAiB,CAACzB,KAAK;QACnC;QACA,OAAOwB;IACT;IAEA,IAAIb,kBAAkB;QACpBN,WAAWiB,cAAcX;IAC3B;IAEA,IAAI,CAACN,UAAU;QACbA,WAAWiB,cAAcR;IAC3B;IAEA,IAAI,CAACT,UAAU;QACbP,iCAAAA,cAAeiC,GAAG,CAAC3B,UAAU;QAC7B,OAAO;IACT;IAEAC,WAAWG,aAAI,CAACC,IAAI,CAACF,iBAAiBF;IAEtCP,iCAAAA,cAAeiC,GAAG,CAAC3B,UAAUC;IAC7B,OAAOA;AACT;AAEO,SAASb,YACdQ,IAAY,EACZC,OAAe,EACfC,OAA6B,EAC7BC,SAAkB;IAElB,MAAME,WAAWd,iBAAiBS,MAAMC,SAASC,SAASC;IAE1D,IAAI,CAACE,UAAU;QACb,MAAM,IAAIgB,wBAAiB,CAACrB;IAC9B;IAEA,OAAOK;AACT;AAEO,eAAeZ,YACpBO,IAAY,EACZC,OAAe,EACfE,SAAkB;IAElB,MAAME,WAAWb,YAAYQ,MAAMC,SAAS+B,WAAW7B;IACvD,IAAIE,SAAS4B,QAAQ,CAAC,UAAU;QAC9B,OAAOC,YAAQ,CAACC,QAAQ,CAAC9B,UAAU,QAAQ+B,KAAK,CAAC,CAAClB;YAChD,MAAM,IAAImB,wBAAiB,CAACrC,MAAMkB,IAAIoB,OAAO;QAC/C;IACF;IAEA,MAAMC,MAAM5C,QAAQC,GAAG,CAAC4C,YAAY,GAEhCC,wBAAwBpC,YACxBqC,QAAQrC;IACZ,OAAOkC;AACT", "ignoreList": [0]}
{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-url.ts"], "sourcesContent": ["import path from '../../../shared/lib/isomorphic/path'\nimport type { MetadataContext } from '../types/resolvers'\n\nfunction isStringOrURL(icon: any): icon is string | URL {\n  return typeof icon === 'string' || icon instanceof URL\n}\n\nfunction createLocalMetadataBase() {\n  // Check if experimental HTTPS is enabled\n  const isExperimentalHttps = Boolean(process.env.__NEXT_EXPERIMENTAL_HTTPS)\n  const protocol = isExperimentalHttps ? 'https' : 'http'\n  return new URL(`${protocol}://localhost:${process.env.PORT || 3000}`)\n}\n\nfunction getPreviewDeploymentUrl(): URL | undefined {\n  const origin = process.env.VERCEL_BRANCH_URL || process.env.VERCEL_URL\n  return origin ? new URL(`https://${origin}`) : undefined\n}\n\nfunction getProductionDeploymentUrl(): URL | undefined {\n  const origin = process.env.VERCEL_PROJECT_PRODUCTION_URL\n  return origin ? new URL(`https://${origin}`) : undefined\n}\n\n/**\n * Given an optional user-provided metadataBase, this determines what the metadataBase should\n * fallback to. Specifically:\n * - In dev, it should always be localhost\n * - In Vercel preview builds, it should be the preview build ID\n * - In start, it should be the user-provided metadataBase value. Otherwise,\n * it'll fall back to the Vercel production deployment, and localhost as a last resort.\n */\nexport function getSocialImageMetadataBaseFallback(\n  metadataBase: URL | null\n): URL {\n  const defaultMetadataBase = createLocalMetadataBase()\n  const previewDeploymentUrl = getPreviewDeploymentUrl()\n  const productionDeploymentUrl = getProductionDeploymentUrl()\n\n  let fallbackMetadataBase\n  if (process.env.NODE_ENV === 'development') {\n    fallbackMetadataBase = defaultMetadataBase\n  } else {\n    fallbackMetadataBase =\n      process.env.NODE_ENV === 'production' &&\n      previewDeploymentUrl &&\n      process.env.VERCEL_ENV === 'preview'\n        ? previewDeploymentUrl\n        : metadataBase || productionDeploymentUrl || defaultMetadataBase\n  }\n\n  return fallbackMetadataBase\n}\n\nfunction resolveUrl(url: null | undefined, metadataBase: URL | null): null\nfunction resolveUrl(url: string | URL, metadataBase: URL | null): URL\nfunction resolveUrl(\n  url: string | URL | null | undefined,\n  metadataBase: URL | null\n): URL | null\nfunction resolveUrl(\n  url: string | URL | null | undefined,\n  metadataBase: URL | null\n): URL | null {\n  if (url instanceof URL) return url\n  if (!url) return null\n\n  try {\n    // If we can construct a URL instance from url, ignore metadataBase\n    const parsedUrl = new URL(url)\n    return parsedUrl\n  } catch {}\n\n  if (!metadataBase) {\n    metadataBase = createLocalMetadataBase()\n  }\n\n  // Handle relative or absolute paths\n  const pathname = metadataBase.pathname || ''\n  const joinedPath = path.posix.join(pathname, url)\n\n  return new URL(joinedPath, metadataBase)\n}\n\n// Resolve with `pathname` if `url` is a relative path.\nfunction resolveRelativeUrl(url: string | URL, pathname: string): string | URL {\n  if (typeof url === 'string' && url.startsWith('./')) {\n    return path.posix.resolve(pathname, url)\n  }\n  return url\n}\n\n// The regex is matching logic from packages/next/src/lib/load-custom-routes.ts\nconst FILE_REGEX =\n  /^(?:\\/((?!\\.well-known(?:\\/.*)?)(?:[^/]+\\/)*[^/]+\\.\\w+))(\\/?|$)/i\nfunction isFilePattern(pathname: string): boolean {\n  return FILE_REGEX.test(pathname)\n}\n\n// Resolve `pathname` if `url` is a relative path the compose with `metadataBase`.\nfunction resolveAbsoluteUrlWithPathname(\n  url: string | URL,\n  metadataBase: URL | null,\n  pathname: string,\n  { trailingSlash }: MetadataContext\n): string {\n  // Resolve url with pathname that always starts with `/`\n  url = resolveRelativeUrl(url, pathname)\n\n  // Convert string url or URL instance to absolute url string,\n  // if there's case needs to be resolved with metadataBase\n  let resolvedUrl = ''\n  const result = metadataBase ? resolveUrl(url, metadataBase) : url\n  if (typeof result === 'string') {\n    resolvedUrl = result\n  } else {\n    resolvedUrl = result.pathname === '/' ? result.origin : result.href\n  }\n\n  // Add trailing slash if it's enabled for urls matches the condition\n  // - Not external, same origin with metadataBase\n  // - Doesn't have query\n  if (trailingSlash && !resolvedUrl.endsWith('/')) {\n    let isRelative = resolvedUrl.startsWith('/')\n    let hasQuery = resolvedUrl.includes('?')\n    let isExternal = false\n    let isFileUrl = false\n\n    if (!isRelative) {\n      try {\n        const parsedUrl = new URL(resolvedUrl)\n        isExternal =\n          metadataBase != null && parsedUrl.origin !== metadataBase.origin\n        isFileUrl = isFilePattern(parsedUrl.pathname)\n      } catch {\n        // If it's not a valid URL, treat it as external\n        isExternal = true\n      }\n      if (\n        // Do not apply trailing slash for file like urls, aligning with the behavior with `trailingSlash`\n        !isFileUrl &&\n        !isExternal &&\n        !hasQuery\n      )\n        return `${resolvedUrl}/`\n    }\n  }\n\n  return resolvedUrl\n}\n\nexport {\n  isStringOrURL,\n  resolveUrl,\n  resolveRelativeUrl,\n  resolveAbsoluteUrlWithPathname,\n}\n"], "names": ["getSocialImageMetadataBaseFallback", "isStringOrURL", "resolveAbsoluteUrlWithPathname", "resolveRelativeUrl", "resolveUrl", "icon", "URL", "createLocalMetadataBase", "isExperimentalHttps", "Boolean", "process", "env", "__NEXT_EXPERIMENTAL_HTTPS", "protocol", "PORT", "getPreviewDeploymentUrl", "origin", "VERCEL_BRANCH_URL", "VERCEL_URL", "undefined", "getProductionDeploymentUrl", "VERCEL_PROJECT_PRODUCTION_URL", "metadataBase", "defaultMetadataBase", "previewDeploymentUrl", "productionDeploymentUrl", "fallbackMetadataBase", "NODE_ENV", "VERCEL_ENV", "url", "parsedUrl", "pathname", "joinedPath", "path", "posix", "join", "startsWith", "resolve", "FILE_REGEX", "isFilePattern", "test", "trailingSlash", "resolvedUrl", "result", "href", "endsWith", "isRelative", "<PERSON><PERSON><PERSON><PERSON>", "includes", "isExternal", "isFileUrl"], "mappings": ";;;;;;;;;;;;;;;;;;IAgCgBA,kCAAkC;eAAlCA;;IAwHdC,aAAa;eAAbA;;IAGAC,8BAA8B;eAA9BA;;IADAC,kBAAkB;eAAlBA;;IADAC,UAAU;eAAVA;;;6DAzJe;;;;;;AAGjB,SAASH,cAAcI,IAAS;IAC9B,OAAO,OAAOA,SAAS,YAAYA,gBAAgBC;AACrD;AAEA,SAASC;IACP,yCAAyC;IACzC,MAAMC,sBAAsBC,QAAQC,QAAQC,GAAG,CAACC,yBAAyB;IACzE,MAAMC,WAAWL,sBAAsB,UAAU;IACjD,OAAO,IAAIF,IAAI,GAAGO,SAAS,aAAa,EAAEH,QAAQC,GAAG,CAACG,IAAI,IAAI,MAAM;AACtE;AAEA,SAASC;IACP,MAAMC,SAASN,QAAQC,GAAG,CAACM,iBAAiB,IAAIP,QAAQC,GAAG,CAACO,UAAU;IACtE,OAAOF,SAAS,IAAIV,IAAI,CAAC,QAAQ,EAAEU,QAAQ,IAAIG;AACjD;AAEA,SAASC;IACP,MAAMJ,SAASN,QAAQC,GAAG,CAACU,6BAA6B;IACxD,OAAOL,SAAS,IAAIV,IAAI,CAAC,QAAQ,EAAEU,QAAQ,IAAIG;AACjD;AAUO,SAASnB,mCACdsB,YAAwB;IAExB,MAAMC,sBAAsBhB;IAC5B,MAAMiB,uBAAuBT;IAC7B,MAAMU,0BAA0BL;IAEhC,IAAIM;IACJ,IAAIhB,QAAQC,GAAG,CAACgB,QAAQ,KAAK,eAAe;QAC1CD,uBAAuBH;IACzB,OAAO;QACLG,uBACEhB,QAAQC,GAAG,CAACgB,QAAQ,KAAK,gBACzBH,wBACAd,QAAQC,GAAG,CAACiB,UAAU,KAAK,YACvBJ,uBACAF,gBAAgBG,2BAA2BF;IACnD;IAEA,OAAOG;AACT;AAQA,SAAStB,WACPyB,GAAoC,EACpCP,YAAwB;IAExB,IAAIO,eAAevB,KAAK,OAAOuB;IAC/B,IAAI,CAACA,KAAK,OAAO;IAEjB,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,IAAIxB,IAAIuB;QAC1B,OAAOC;IACT,EAAE,OAAM,CAAC;IAET,IAAI,CAACR,cAAc;QACjBA,eAAef;IACjB;IAEA,oCAAoC;IACpC,MAAMwB,WAAWT,aAAaS,QAAQ,IAAI;IAC1C,MAAMC,aAAaC,aAAI,CAACC,KAAK,CAACC,IAAI,CAACJ,UAAUF;IAE7C,OAAO,IAAIvB,IAAI0B,YAAYV;AAC7B;AAEA,uDAAuD;AACvD,SAASnB,mBAAmB0B,GAAiB,EAAEE,QAAgB;IAC7D,IAAI,OAAOF,QAAQ,YAAYA,IAAIO,UAAU,CAAC,OAAO;QACnD,OAAOH,aAAI,CAACC,KAAK,CAACG,OAAO,CAACN,UAAUF;IACtC;IACA,OAAOA;AACT;AAEA,+EAA+E;AAC/E,MAAMS,aACJ;AACF,SAASC,cAAcR,QAAgB;IACrC,OAAOO,WAAWE,IAAI,CAACT;AACzB;AAEA,kFAAkF;AAClF,SAAS7B,+BACP2B,GAAiB,EACjBP,YAAwB,EACxBS,QAAgB,EAChB,EAAEU,aAAa,EAAmB;IAElC,wDAAwD;IACxDZ,MAAM1B,mBAAmB0B,KAAKE;IAE9B,6DAA6D;IAC7D,yDAAyD;IACzD,IAAIW,cAAc;IAClB,MAAMC,SAASrB,eAAelB,WAAWyB,KAAKP,gBAAgBO;IAC9D,IAAI,OAAOc,WAAW,UAAU;QAC9BD,cAAcC;IAChB,OAAO;QACLD,cAAcC,OAAOZ,QAAQ,KAAK,MAAMY,OAAO3B,MAAM,GAAG2B,OAAOC,IAAI;IACrE;IAEA,oEAAoE;IACpE,gDAAgD;IAChD,uBAAuB;IACvB,IAAIH,iBAAiB,CAACC,YAAYG,QAAQ,CAAC,MAAM;QAC/C,IAAIC,aAAaJ,YAAYN,UAAU,CAAC;QACxC,IAAIW,WAAWL,YAAYM,QAAQ,CAAC;QACpC,IAAIC,aAAa;QACjB,IAAIC,YAAY;QAEhB,IAAI,CAACJ,YAAY;YACf,IAAI;gBACF,MAAMhB,YAAY,IAAIxB,IAAIoC;gBAC1BO,aACE3B,gBAAgB,QAAQQ,UAAUd,MAAM,KAAKM,aAAaN,MAAM;gBAClEkC,YAAYX,cAAcT,UAAUC,QAAQ;YAC9C,EAAE,OAAM;gBACN,gDAAgD;gBAChDkB,aAAa;YACf;YACA,IACE,kGAAkG;YAClG,CAACC,aACD,CAACD,cACD,CAACF,UAED,OAAO,GAAGL,YAAY,CAAC,CAAC;QAC5B;IACF;IAEA,OAAOA;AACT", "ignoreList": [0]}
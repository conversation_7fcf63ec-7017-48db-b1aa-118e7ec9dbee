{"version": 3, "sources": ["../../../src/client/dev/report-hmr-latency.ts"], "sourcesContent": ["declare global {\n  interface Window {\n    __NEXT_HMR_LATENCY_CB: ((latencyMs: number) => void) | undefined\n  }\n}\n\n/**\n * Logs information about a completed HMR to the console, the server (via a\n * `client-hmr-latency` event), and to `self.__NEXT_HMR_LATENCY_CB` (a debugging\n * hook).\n *\n * @param hasUpdate Set this to `false` to avoid reporting the HMR event via a\n *   `client-hmr-latency` event or to `self.__NEXT_HMR_LATENCY_CB`. Used by\n *   turbopack when we must report a message to the browser console (because we\n *   already logged a \"rebuilding\" message), but it's not a real HMR, so we\n *   don't want to impact our telemetry.\n */\nexport default function reportHmrLatency(\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string | number>,\n  startMsSinceEpoch: number,\n  endMsSinceEpoch: number,\n  hasUpdate: boolean = true\n) {\n  const latencyMs = endMsSinceEpoch - startMsSinceEpoch\n  console.log(`[Fast Refresh] done in ${latencyMs}ms`)\n  if (!hasUpdate) {\n    return\n  }\n  sendMessage(\n    JSON.stringify({\n      event: 'client-hmr-latency',\n      id: window.__nextDevClientId,\n      startTime: startMsSinceEpoch,\n      endTime: endMsSinceEpoch,\n      page: window.location.pathname,\n      updatedModules,\n      // Whether the page (tab) was hidden at the time the event occurred.\n      // This can impact the accuracy of the event's timing.\n      isPageHidden: document.visibilityState === 'hidden',\n    })\n  )\n  if (self.__NEXT_HMR_LATENCY_CB) {\n    self.__NEXT_HMR_LATENCY_CB(latencyMs)\n  }\n}\n"], "names": ["reportHmrLatency", "sendMessage", "updatedModules", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdate", "latencyMs", "console", "log", "JSON", "stringify", "event", "id", "window", "__nextDevClientId", "startTime", "endTime", "page", "location", "pathname", "isPageHidden", "document", "visibilityState", "self", "__NEXT_HMR_LATENCY_CB"], "mappings": ";;;;+BAMA;;;;;;;;;;CAUC,GACD;;;eAAwBA;;;AAAT,SAASA,iBACtBC,WAAsC,EACtCC,cAA8C,EAC9CC,iBAAyB,EACzBC,eAAuB,EACvBC,SAAyB;IAAzBA,IAAAA,sBAAAA,YAAqB;IAErB,MAAMC,YAAYF,kBAAkBD;IACpCI,QAAQC,GAAG,CAAC,AAAC,4BAAyBF,YAAU;IAChD,IAAI,CAACD,WAAW;QACd;IACF;IACAJ,YACEQ,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,IAAIC,OAAOC,iBAAiB;QAC5BC,WAAWZ;QACXa,SAASZ;QACTa,MAAMJ,OAAOK,QAAQ,CAACC,QAAQ;QAC9BjB;QACA,oEAAoE;QACpE,sDAAsD;QACtDkB,cAAcC,SAASC,eAAe,KAAK;IAC7C;IAEF,IAAIC,KAAKC,qBAAqB,EAAE;QAC9BD,KAAKC,qBAAqB,CAAClB;IAC7B;AACF", "ignoreList": [0]}
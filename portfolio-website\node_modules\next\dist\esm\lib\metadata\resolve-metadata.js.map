{"version": 3, "sources": ["../../../src/lib/metadata/resolve-metadata.ts"], "sourcesContent": ["import type {\n  Metada<PERSON>,\n  ResolvedMetadata,\n  ResolvedViewport,\n  ResolvingMetadata,\n  ResolvingViewport,\n  Viewport,\n} from './types/metadata-interface'\nimport type { MetadataImageModule } from '../../build/webpack/loaders/metadata/types'\nimport type { GetDynamicParamFromSegment } from '../../server/app-render/app-render'\nimport type { Twitter } from './types/twitter-types'\nimport type { OpenGraph } from './types/opengraph-types'\nimport type { AppDirModules } from '../../build/webpack/loaders/next-app-loader'\nimport type { MetadataContext } from './types/resolvers'\nimport type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type {\n  AbsoluteTemplateString,\n  IconDescriptor,\n  ResolvedIcons,\n} from './types/metadata-types'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { StaticMetadata } from './types/icons'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport type { Params } from '../../server/request/params'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport 'server-only'\n\nimport { cache } from 'react'\nimport {\n  createDefaultMetadata,\n  createDefaultViewport,\n} from './default-metadata'\nimport { resolveOpenGraph, resolveTwitter } from './resolvers/resolve-opengraph'\nimport { resolveTitle } from './resolvers/resolve-title'\nimport { resolveAsArrayOrUndefined } from './generate/utils'\nimport {\n  getComponentTypeModule,\n  getLayoutOrPageModule,\n} from '../../server/lib/app-dir-module'\nimport { interopDefault } from '../interop-default'\nimport {\n  resolveAlternates,\n  resolveAppleWebApp,\n  resolveAppLinks,\n  resolveRobots,\n  resolveThemeColor,\n  resolveVerification,\n  resolveItunes,\n  resolveFacebook,\n  resolvePagination,\n} from './resolvers/resolve-basics'\nimport { resolveIcons } from './resolvers/resolve-icons'\nimport { getTracer } from '../../server/lib/trace/tracer'\nimport { ResolveMetadataSpan } from '../../server/lib/trace/constants'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport * as Log from '../../build/output/log'\nimport { createServerParamsForMetadata } from '../../server/request/params'\n\ntype StaticIcons = Pick<ResolvedIcons, 'icon' | 'apple'>\n\ntype MetadataResolver = (\n  parent: ResolvingMetadata\n) => Metadata | Promise<Metadata>\ntype ViewportResolver = (\n  parent: ResolvingViewport\n) => Viewport | Promise<Viewport>\n\nexport type MetadataErrorType = 'not-found' | 'forbidden' | 'unauthorized'\n\nexport type MetadataItems = Array<\n  [Metadata | MetadataResolver | null, StaticMetadata]\n>\n\nexport type ViewportItems = Array<Viewport | ViewportResolver | null>\n\ntype TitleTemplates = {\n  title: string | null\n  twitter: string | null\n  openGraph: string | null\n}\n\ntype BuildState = {\n  warnings: Set<string>\n}\n\ntype LayoutProps = {\n  params: { [key: string]: any }\n}\ntype PageProps = {\n  params: { [key: string]: any }\n  searchParams: { [key: string]: any }\n}\n\nfunction isFavicon(icon: IconDescriptor | undefined): boolean {\n  if (!icon) {\n    return false\n  }\n\n  // turbopack appends a hash to all images\n  return (\n    (icon.url === '/favicon.ico' ||\n      icon.url.toString().startsWith('/favicon.ico?')) &&\n    icon.type === 'image/x-icon'\n  )\n}\n\nasync function mergeStaticMetadata(\n  source: Metadata | null,\n  target: ResolvedMetadata,\n  staticFilesMetadata: StaticMetadata,\n  metadataContext: MetadataContext,\n  titleTemplates: TitleTemplates,\n  leafSegmentStaticIcons: StaticIcons,\n  pathname: Promise<string>\n): Promise<ResolvedMetadata> {\n  if (!staticFilesMetadata) return target\n  const { icon, apple, openGraph, twitter, manifest } = staticFilesMetadata\n\n  // Keep updating the static icons in the most leaf node\n\n  if (icon) {\n    leafSegmentStaticIcons.icon = icon\n  }\n  if (apple) {\n    leafSegmentStaticIcons.apple = apple\n  }\n\n  // file based metadata is specified and current level metadata twitter.images is not specified\n  if (twitter && !source?.twitter?.hasOwnProperty('images')) {\n    const resolvedTwitter = resolveTwitter(\n      { ...target.twitter, images: twitter } as Twitter,\n      target.metadataBase,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.twitter\n    )\n    target.twitter = resolvedTwitter\n  }\n\n  // file based metadata is specified and current level metadata openGraph.images is not specified\n  if (openGraph && !source?.openGraph?.hasOwnProperty('images')) {\n    const resolvedOpenGraph = await resolveOpenGraph(\n      { ...target.openGraph, images: openGraph } as OpenGraph,\n      target.metadataBase,\n      pathname,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.openGraph\n    )\n    target.openGraph = resolvedOpenGraph\n  }\n  if (manifest) {\n    target.manifest = manifest\n  }\n\n  return target\n}\n\n// Merge the source metadata into the resolved target metadata.\nasync function mergeMetadata(\n  route: string,\n  pathname: Promise<string>,\n  {\n    source,\n    target,\n    staticFilesMetadata,\n    titleTemplates,\n    metadataContext,\n    buildState,\n    leafSegmentStaticIcons,\n  }: {\n    source: Metadata | null\n    target: ResolvedMetadata\n    staticFilesMetadata: StaticMetadata\n    titleTemplates: TitleTemplates\n    metadataContext: MetadataContext\n    buildState: BuildState\n    leafSegmentStaticIcons: StaticIcons\n  }\n): Promise<ResolvedMetadata> {\n  // If there's override metadata, prefer it otherwise fallback to the default metadata.\n  const metadataBase =\n    typeof source?.metadataBase !== 'undefined'\n      ? source.metadataBase\n      : target.metadataBase\n  for (const key_ in source) {\n    const key = key_ as keyof Metadata\n\n    switch (key) {\n      case 'title': {\n        target.title = resolveTitle(source.title, titleTemplates.title)\n        break\n      }\n      case 'alternates': {\n        target.alternates = await resolveAlternates(\n          source.alternates,\n          metadataBase,\n          pathname,\n          metadataContext\n        )\n        break\n      }\n      case 'openGraph': {\n        target.openGraph = await resolveOpenGraph(\n          source.openGraph,\n          metadataBase,\n          pathname,\n          metadataContext,\n          titleTemplates.openGraph\n        )\n        break\n      }\n      case 'twitter': {\n        target.twitter = resolveTwitter(\n          source.twitter,\n          metadataBase,\n          metadataContext,\n          titleTemplates.twitter\n        )\n        break\n      }\n      case 'facebook':\n        target.facebook = resolveFacebook(source.facebook)\n        break\n      case 'verification':\n        target.verification = resolveVerification(source.verification)\n        break\n\n      case 'icons': {\n        target.icons = resolveIcons(source.icons)\n        break\n      }\n      case 'appleWebApp':\n        target.appleWebApp = resolveAppleWebApp(source.appleWebApp)\n        break\n      case 'appLinks':\n        target.appLinks = resolveAppLinks(source.appLinks)\n        break\n      case 'robots': {\n        target.robots = resolveRobots(source.robots)\n        break\n      }\n      case 'archives':\n      case 'assets':\n      case 'bookmarks':\n      case 'keywords': {\n        target[key] = resolveAsArrayOrUndefined(source[key])\n        break\n      }\n      case 'authors': {\n        target[key] = resolveAsArrayOrUndefined(source.authors)\n        break\n      }\n      case 'itunes': {\n        target[key] = await resolveItunes(\n          source.itunes,\n          metadataBase,\n          pathname,\n          metadataContext\n        )\n        break\n      }\n      case 'pagination': {\n        target.pagination = await resolvePagination(\n          source.pagination,\n          metadataBase,\n          pathname,\n          metadataContext\n        )\n        break\n      }\n      // directly assign fields that fallback to null\n      case 'applicationName':\n      case 'description':\n      case 'generator':\n      case 'creator':\n      case 'publisher':\n      case 'category':\n      case 'classification':\n      case 'referrer':\n      case 'formatDetection':\n      case 'manifest':\n      case 'pinterest':\n        // @ts-ignore TODO: support inferring\n        target[key] = source[key] || null\n        break\n      case 'other':\n        target.other = Object.assign({}, target.other, source.other)\n        break\n      case 'metadataBase':\n        target.metadataBase = metadataBase\n        break\n\n      default: {\n        if (\n          (key === 'viewport' ||\n            key === 'themeColor' ||\n            key === 'colorScheme') &&\n          source[key] != null\n        ) {\n          buildState.warnings.add(\n            `Unsupported metadata ${key} is configured in metadata export in ${route}. Please move it to viewport export instead.\\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport`\n          )\n        }\n        break\n      }\n    }\n  }\n  return mergeStaticMetadata(\n    source,\n    target,\n    staticFilesMetadata,\n    metadataContext,\n    titleTemplates,\n    leafSegmentStaticIcons,\n    pathname\n  )\n}\n\nfunction mergeViewport({\n  target,\n  source,\n}: {\n  target: ResolvedViewport\n  source: Viewport | null\n}): void {\n  if (!source) return\n  for (const key_ in source) {\n    const key = key_ as keyof Viewport\n\n    switch (key) {\n      case 'themeColor': {\n        target.themeColor = resolveThemeColor(source.themeColor)\n        break\n      }\n      case 'colorScheme':\n        target.colorScheme = source.colorScheme || null\n        break\n      default:\n        // always override the target with the source\n        // @ts-ignore viewport properties\n        target[key] = source[key]\n        break\n    }\n  }\n}\n\nfunction getDefinedViewport(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Viewport | ViewportResolver | null {\n  if (typeof mod.generateViewport === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingViewport) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateViewport,\n        {\n          spanName: `generateViewport ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateViewport(props, parent)\n      )\n  }\n  return mod.viewport || null\n}\n\nfunction getDefinedMetadata(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Metadata | MetadataResolver | null {\n  if (typeof mod.generateMetadata === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingMetadata) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateMetadata,\n        {\n          spanName: `generateMetadata ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateMetadata(props, parent)\n      )\n  }\n  return mod.metadata || null\n}\n\nasync function collectStaticImagesFiles(\n  metadata: AppDirModules['metadata'],\n  props: any,\n  type: keyof NonNullable<AppDirModules['metadata']>\n) {\n  if (!metadata?.[type]) return undefined\n\n  const iconPromises = metadata[type as 'icon' | 'apple'].map(\n    async (imageModule: (p: any) => Promise<MetadataImageModule[]>) =>\n      interopDefault(await imageModule(props))\n  )\n\n  return iconPromises?.length > 0\n    ? (await Promise.all(iconPromises))?.flat()\n    : undefined\n}\n\nasync function resolveStaticMetadata(\n  modules: AppDirModules,\n  props: any\n): Promise<StaticMetadata> {\n  const { metadata } = modules\n  if (!metadata) return null\n\n  const [icon, apple, openGraph, twitter] = await Promise.all([\n    collectStaticImagesFiles(metadata, props, 'icon'),\n    collectStaticImagesFiles(metadata, props, 'apple'),\n    collectStaticImagesFiles(metadata, props, 'openGraph'),\n    collectStaticImagesFiles(metadata, props, 'twitter'),\n  ])\n\n  const staticMetadata = {\n    icon,\n    apple,\n    openGraph,\n    twitter,\n    manifest: metadata.manifest,\n  }\n\n  return staticMetadata\n}\n\n// [layout.metadata, static files metadata] -> ... -> [page.metadata, static files metadata]\nasync function collectMetadata({\n  tree,\n  metadataItems,\n  errorMetadataItem,\n  props,\n  route,\n  errorConvention,\n}: {\n  tree: LoaderTree\n  metadataItems: MetadataItems\n  errorMetadataItem: MetadataItems[number]\n  props: any\n  route: string\n  errorConvention?: MetadataErrorType\n}) {\n  let mod\n  let modType\n  const hasErrorConventionComponent = Boolean(\n    errorConvention && tree[2][errorConvention]\n  )\n  if (errorConvention) {\n    mod = await getComponentTypeModule(tree, 'layout')\n    modType = errorConvention\n  } else {\n    const { mod: layoutOrPageMod, modType: layoutOrPageModType } =\n      await getLayoutOrPageModule(tree)\n    mod = layoutOrPageMod\n    modType = layoutOrPageModType\n  }\n\n  if (modType) {\n    route += `/${modType}`\n  }\n\n  const staticFilesMetadata = await resolveStaticMetadata(tree[2], props)\n  const metadataExport = mod ? getDefinedMetadata(mod, props, { route }) : null\n\n  metadataItems.push([metadataExport, staticFilesMetadata])\n\n  if (hasErrorConventionComponent && errorConvention) {\n    const errorMod = await getComponentTypeModule(tree, errorConvention)\n    const errorMetadataExport = errorMod\n      ? getDefinedMetadata(errorMod, props, { route })\n      : null\n\n    errorMetadataItem[0] = errorMetadataExport\n    errorMetadataItem[1] = staticFilesMetadata\n  }\n}\n\n// [layout.metadata, static files metadata] -> ... -> [page.metadata, static files metadata]\nasync function collectViewport({\n  tree,\n  viewportItems,\n  errorViewportItemRef,\n  props,\n  route,\n  errorConvention,\n}: {\n  tree: LoaderTree\n  viewportItems: ViewportItems\n  errorViewportItemRef: ErrorViewportItemRef\n  props: any\n  route: string\n  errorConvention?: MetadataErrorType\n}) {\n  let mod\n  let modType\n  const hasErrorConventionComponent = Boolean(\n    errorConvention && tree[2][errorConvention]\n  )\n  if (errorConvention) {\n    mod = await getComponentTypeModule(tree, 'layout')\n    modType = errorConvention\n  } else {\n    const { mod: layoutOrPageMod, modType: layoutOrPageModType } =\n      await getLayoutOrPageModule(tree)\n    mod = layoutOrPageMod\n    modType = layoutOrPageModType\n  }\n\n  if (modType) {\n    route += `/${modType}`\n  }\n\n  const viewportExport = mod ? getDefinedViewport(mod, props, { route }) : null\n\n  viewportItems.push(viewportExport)\n\n  if (hasErrorConventionComponent && errorConvention) {\n    const errorMod = await getComponentTypeModule(tree, errorConvention)\n    const errorViewportExport = errorMod\n      ? getDefinedViewport(errorMod, props, { route })\n      : null\n\n    errorViewportItemRef.current = errorViewportExport\n  }\n}\n\nconst resolveMetadataItems = cache(async function (\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n) {\n  const parentParams = {}\n  const metadataItems: MetadataItems = []\n  const errorMetadataItem: MetadataItems[number] = [null, null]\n  const treePrefix = undefined\n  return resolveMetadataItemsImpl(\n    metadataItems,\n    tree,\n    treePrefix,\n    parentParams,\n    searchParams,\n    errorConvention,\n    errorMetadataItem,\n    getDynamicParamFromSegment,\n    workStore\n  )\n})\n\nasync function resolveMetadataItemsImpl(\n  metadataItems: MetadataItems,\n  tree: LoaderTree,\n  /** Provided tree can be nested subtree, this argument says what is the path of such subtree */\n  treePrefix: undefined | string[],\n  parentParams: Params,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  errorMetadataItem: MetadataItems[number],\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<MetadataItems> {\n  const [segment, parallelRoutes, { page }] = tree\n  const currentTreePrefix =\n    treePrefix && treePrefix.length ? [...treePrefix, segment] : [segment]\n  const isPage = typeof page !== 'undefined'\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n  /**\n   * Create object holding the parent params and current params\n   */\n  let currentParams = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const params = createServerParamsForMetadata(currentParams, workStore)\n\n  let layerProps: LayoutProps | PageProps\n  if (isPage) {\n    layerProps = {\n      params,\n      searchParams,\n    }\n  } else {\n    layerProps = {\n      params,\n    }\n  }\n\n  await collectMetadata({\n    tree,\n    metadataItems,\n    errorMetadataItem,\n    errorConvention,\n    props: layerProps,\n    route: currentTreePrefix\n      // __PAGE__ shouldn't be shown in a route\n      .filter((s) => s !== PAGE_SEGMENT_KEY)\n      .join('/'),\n  })\n\n  for (const key in parallelRoutes) {\n    const childTree = parallelRoutes[key]\n    await resolveMetadataItemsImpl(\n      metadataItems,\n      childTree,\n      currentTreePrefix,\n      currentParams,\n      searchParams,\n      errorConvention,\n      errorMetadataItem,\n      getDynamicParamFromSegment,\n      workStore\n    )\n  }\n\n  if (Object.keys(parallelRoutes).length === 0 && errorConvention) {\n    // If there are no parallel routes, place error metadata as the last item.\n    // e.g. layout -> layout -> not-found\n    metadataItems.push(errorMetadataItem)\n  }\n\n  return metadataItems\n}\n\ntype ErrorViewportItemRef = { current: ViewportItems[number] }\nconst resolveViewportItems = cache(async function (\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n) {\n  const parentParams = {}\n  const viewportItems: ViewportItems = []\n  const errorViewportItemRef: ErrorViewportItemRef = {\n    current: null,\n  }\n  const treePrefix = undefined\n  return resolveViewportItemsImpl(\n    viewportItems,\n    tree,\n    treePrefix,\n    parentParams,\n    searchParams,\n    errorConvention,\n    errorViewportItemRef,\n    getDynamicParamFromSegment,\n    workStore\n  )\n})\n\nasync function resolveViewportItemsImpl(\n  viewportItems: ViewportItems,\n  tree: LoaderTree,\n  /** Provided tree can be nested subtree, this argument says what is the path of such subtree */\n  treePrefix: undefined | string[],\n  parentParams: Params,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  errorViewportItemRef: ErrorViewportItemRef,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<ViewportItems> {\n  const [segment, parallelRoutes, { page }] = tree\n  const currentTreePrefix =\n    treePrefix && treePrefix.length ? [...treePrefix, segment] : [segment]\n  const isPage = typeof page !== 'undefined'\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n  /**\n   * Create object holding the parent params and current params\n   */\n  let currentParams = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const params = createServerParamsForMetadata(currentParams, workStore)\n\n  let layerProps: LayoutProps | PageProps\n  if (isPage) {\n    layerProps = {\n      params,\n      searchParams,\n    }\n  } else {\n    layerProps = {\n      params,\n    }\n  }\n\n  await collectViewport({\n    tree,\n    viewportItems,\n    errorViewportItemRef,\n    errorConvention,\n    props: layerProps,\n    route: currentTreePrefix\n      // __PAGE__ shouldn't be shown in a route\n      .filter((s) => s !== PAGE_SEGMENT_KEY)\n      .join('/'),\n  })\n\n  for (const key in parallelRoutes) {\n    const childTree = parallelRoutes[key]\n    await resolveViewportItemsImpl(\n      viewportItems,\n      childTree,\n      currentTreePrefix,\n      currentParams,\n      searchParams,\n      errorConvention,\n      errorViewportItemRef,\n      getDynamicParamFromSegment,\n      workStore\n    )\n  }\n\n  if (Object.keys(parallelRoutes).length === 0 && errorConvention) {\n    // If there are no parallel routes, place error metadata as the last item.\n    // e.g. layout -> layout -> not-found\n    viewportItems.push(errorViewportItemRef.current)\n  }\n\n  return viewportItems\n}\n\ntype WithTitle = { title?: AbsoluteTemplateString | null }\ntype WithDescription = { description?: string | null }\n\nconst isTitleTruthy = (title: AbsoluteTemplateString | null | undefined) =>\n  !!title?.absolute\nconst hasTitle = (metadata: WithTitle | null) => isTitleTruthy(metadata?.title)\n\nfunction inheritFromMetadata(\n  target: (WithTitle & WithDescription) | null,\n  metadata: ResolvedMetadata\n) {\n  if (target) {\n    if (!hasTitle(target) && hasTitle(metadata)) {\n      target.title = metadata.title\n    }\n    if (!target.description && metadata.description) {\n      target.description = metadata.description\n    }\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst commonOgKeys = ['title', 'description', 'images'] as const\nfunction postProcessMetadata(\n  metadata: ResolvedMetadata,\n  favicon: any,\n  titleTemplates: TitleTemplates,\n  metadataContext: MetadataContext\n): ResolvedMetadata {\n  const { openGraph, twitter } = metadata\n\n  if (openGraph) {\n    // If there's openGraph information but not configured in twitter,\n    // inherit them from openGraph metadata.\n    let autoFillProps: Partial<{\n      [Key in (typeof commonOgKeys)[number]]: NonNullable<\n        ResolvedMetadata['openGraph']\n      >[Key]\n    }> = {}\n    const hasTwTitle = hasTitle(twitter)\n    const hasTwDescription = twitter?.description\n    const hasTwImages = Boolean(\n      twitter?.hasOwnProperty('images') && twitter.images\n    )\n    if (!hasTwTitle) {\n      if (isTitleTruthy(openGraph.title)) {\n        autoFillProps.title = openGraph.title\n      } else if (metadata.title && isTitleTruthy(metadata.title)) {\n        autoFillProps.title = metadata.title\n      }\n    }\n    if (!hasTwDescription)\n      autoFillProps.description =\n        openGraph.description || metadata.description || undefined\n    if (!hasTwImages) autoFillProps.images = openGraph.images\n\n    if (Object.keys(autoFillProps).length > 0) {\n      const partialTwitter = resolveTwitter(\n        autoFillProps,\n        metadata.metadataBase,\n        metadataContext,\n        titleTemplates.twitter\n      )\n      if (metadata.twitter) {\n        metadata.twitter = Object.assign({}, metadata.twitter, {\n          ...(!hasTwTitle && { title: partialTwitter?.title }),\n          ...(!hasTwDescription && {\n            description: partialTwitter?.description,\n          }),\n          ...(!hasTwImages && { images: partialTwitter?.images }),\n        })\n      } else {\n        metadata.twitter = partialTwitter\n      }\n    }\n  }\n\n  // If there's no title and description configured in openGraph or twitter,\n  // use the title and description from metadata.\n  inheritFromMetadata(openGraph, metadata)\n  inheritFromMetadata(twitter, metadata)\n\n  if (favicon) {\n    if (!metadata.icons) {\n      metadata.icons = {\n        icon: [],\n        apple: [],\n      }\n    }\n\n    metadata.icons.icon.unshift(favicon)\n  }\n\n  return metadata\n}\n\ntype Result<T> = null | T | Promise<null | T> | PromiseLike<null | T>\n\nfunction prerenderMetadata(metadataItems: MetadataItems) {\n  // If the index is a function then it is a resolver and the next slot\n  // is the corresponding result. If the index is not a function it is the result\n  // itself.\n  const resolversAndResults: Array<\n    ((value: ResolvedMetadata) => void) | Result<Metadata>\n  > = []\n  for (let i = 0; i < metadataItems.length; i++) {\n    const metadataExport = metadataItems[i][0]\n    getResult(resolversAndResults, metadataExport)\n  }\n  return resolversAndResults\n}\n\nfunction prerenderViewport(viewportItems: ViewportItems) {\n  // If the index is a function then it is a resolver and the next slot\n  // is the corresponding result. If the index is not a function it is the result\n  // itself.\n  const resolversAndResults: Array<\n    ((value: ResolvedViewport) => void) | Result<Viewport>\n  > = []\n  for (let i = 0; i < viewportItems.length; i++) {\n    const viewportExport = viewportItems[i]\n    getResult(resolversAndResults, viewportExport)\n  }\n  return resolversAndResults\n}\n\ntype Resolved<T> = T extends Metadata ? ResolvedMetadata : ResolvedViewport\n\nfunction getResult<T extends Metadata | Viewport>(\n  resolversAndResults: Array<((value: Resolved<T>) => void) | Result<T>>,\n  exportForResult: null | T | ((parent: Promise<Resolved<T>>) => Result<T>)\n) {\n  if (typeof exportForResult === 'function') {\n    const result = exportForResult(\n      new Promise<Resolved<T>>((resolve) => resolversAndResults.push(resolve))\n    )\n    resolversAndResults.push(result)\n    if (result instanceof Promise) {\n      // since we eager execute generateMetadata and\n      // they can reject at anytime we need to ensure\n      // we attach the catch handler right away to\n      // prevent unhandled rejections crashing the process\n      result.catch((err) => {\n        return {\n          __nextError: err,\n        }\n      })\n    }\n  } else if (typeof exportForResult === 'object') {\n    resolversAndResults.push(exportForResult)\n  } else {\n    resolversAndResults.push(null)\n  }\n}\n\nfunction resolvePendingResult<\n  ResolvedType extends ResolvedMetadata | ResolvedViewport,\n>(\n  parentResult: ResolvedType,\n  resolveParentResult: (value: ResolvedType) => void\n): void {\n  // In dev we clone and freeze to prevent relying on mutating resolvedMetadata directly.\n  // In prod we just pass resolvedMetadata through without any copying.\n  if (process.env.NODE_ENV === 'development') {\n    // @ts-expect-error -- DeepReadonly<T> is by definition not assignable to T\n    // Instead, we should only accept DeepReadonly<ResolvedType>\n    parentResult = (\n      require('../../shared/lib/deep-freeze') as typeof import('../../shared/lib/deep-freeze')\n    ).deepFreeze(\n      (\n        require('./clone-metadata') as typeof import('./clone-metadata')\n      ).cloneMetadata(parentResult)\n    )\n  }\n\n  resolveParentResult(parentResult)\n}\n\nexport async function accumulateMetadata(\n  route: string,\n  metadataItems: MetadataItems,\n  pathname: Promise<string>,\n  metadataContext: MetadataContext\n): Promise<ResolvedMetadata> {\n  let resolvedMetadata = createDefaultMetadata()\n\n  let titleTemplates: TitleTemplates = {\n    title: null,\n    twitter: null,\n    openGraph: null,\n  }\n\n  const buildState = {\n    warnings: new Set<string>(),\n  }\n\n  let favicon\n\n  // Collect the static icons in the most leaf node,\n  // since we don't collect all the static metadata icons in the parent segments.\n  const leafSegmentStaticIcons = {\n    icon: [],\n    apple: [],\n  }\n\n  const resolversAndResults = prerenderMetadata(metadataItems)\n  let resultIndex = 0\n\n  for (let i = 0; i < metadataItems.length; i++) {\n    const staticFilesMetadata = metadataItems[i][1]\n    // Treat favicon as special case, it should be the first icon in the list\n    // i <= 1 represents root layout, and if current page is also at root\n    if (i <= 1 && isFavicon(staticFilesMetadata?.icon?.[0])) {\n      const iconMod = staticFilesMetadata?.icon?.shift()\n      if (i === 0) favicon = iconMod\n    }\n\n    let pendingMetadata = resolversAndResults[resultIndex++]\n    if (typeof pendingMetadata === 'function') {\n      // This metadata item had a `generateMetadata` and\n      // we need to provide the currently resolved metadata\n      // to it before we continue;\n      const resolveParentMetadata = pendingMetadata\n      // we know that the next item is a result if this item\n      // was a resolver\n      pendingMetadata = resolversAndResults[resultIndex++] as Result<Metadata>\n\n      resolvePendingResult(resolvedMetadata, resolveParentMetadata)\n    }\n    // Otherwise the item was either null or a static export\n\n    let metadata: Metadata | null\n    if (isPromiseLike(pendingMetadata)) {\n      metadata = await pendingMetadata\n    } else {\n      metadata = pendingMetadata\n    }\n\n    resolvedMetadata = await mergeMetadata(route, pathname, {\n      target: resolvedMetadata,\n      source: metadata,\n      metadataContext,\n      staticFilesMetadata,\n      titleTemplates,\n      buildState,\n      leafSegmentStaticIcons,\n    })\n\n    // If the layout is the same layer with page, skip the leaf layout and leaf page\n    // The leaf layout and page are the last two items\n    if (i < metadataItems.length - 2) {\n      titleTemplates = {\n        title: resolvedMetadata.title?.template || null,\n        openGraph: resolvedMetadata.openGraph?.title.template || null,\n        twitter: resolvedMetadata.twitter?.title.template || null,\n      }\n    }\n  }\n\n  if (\n    leafSegmentStaticIcons.icon.length > 0 ||\n    leafSegmentStaticIcons.apple.length > 0\n  ) {\n    if (!resolvedMetadata.icons) {\n      resolvedMetadata.icons = {\n        icon: [],\n        apple: [],\n      }\n      if (leafSegmentStaticIcons.icon.length > 0) {\n        resolvedMetadata.icons.icon.unshift(...leafSegmentStaticIcons.icon)\n      }\n      if (leafSegmentStaticIcons.apple.length > 0) {\n        resolvedMetadata.icons.apple.unshift(...leafSegmentStaticIcons.apple)\n      }\n    }\n  }\n\n  // Only log warnings if there are any, and only once after the metadata resolving process is finished\n  if (buildState.warnings.size > 0) {\n    for (const warning of buildState.warnings) {\n      Log.warn(warning)\n    }\n  }\n\n  return postProcessMetadata(\n    resolvedMetadata,\n    favicon,\n    titleTemplates,\n    metadataContext\n  )\n}\n\nexport async function accumulateViewport(\n  viewportItems: ViewportItems\n): Promise<ResolvedViewport> {\n  const resolvedViewport: ResolvedViewport = createDefaultViewport()\n\n  const resolversAndResults = prerenderViewport(viewportItems)\n  let i = 0\n\n  while (i < resolversAndResults.length) {\n    let pendingViewport = resolversAndResults[i++]\n    if (typeof pendingViewport === 'function') {\n      // this viewport item had a `generateViewport` and\n      // we need to provide the currently resolved viewport\n      // to it before we continue;\n      const resolveParentViewport = pendingViewport\n      // we know that the next item is a result if this item\n      // was a resolver\n      pendingViewport = resolversAndResults[i++] as Result<Viewport>\n\n      resolvePendingResult(resolvedViewport, resolveParentViewport)\n    }\n    // Otherwise the item was either null or a static export\n\n    let viewport: Viewport | null\n    if (isPromiseLike(pendingViewport)) {\n      viewport = await pendingViewport\n    } else {\n      viewport = pendingViewport\n    }\n\n    mergeViewport({\n      target: resolvedViewport,\n      source: viewport,\n    })\n  }\n  return resolvedViewport\n}\n\n// Exposed API for metadata component, that directly resolve the loader tree and related context as resolved metadata.\nexport async function resolveMetadata(\n  tree: LoaderTree,\n  pathname: Promise<string>,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore,\n  metadataContext: MetadataContext\n): Promise<ResolvedMetadata> {\n  const metadataItems = await resolveMetadataItems(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore\n  )\n  return accumulateMetadata(\n    workStore.route,\n    metadataItems,\n    pathname,\n    metadataContext\n  )\n}\n\n// Exposed API for viewport component, that directly resolve the loader tree and related context as resolved viewport.\nexport async function resolveViewport(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<ResolvedViewport> {\n  const viewportItems = await resolveViewportItems(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore\n  )\n  return accumulateViewport(viewportItems)\n}\n\nfunction isPromiseLike<T>(\n  value: unknown | PromiseLike<T>\n): value is PromiseLike<T> {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    typeof (value as PromiseLike<unknown>).then === 'function'\n  )\n}\n"], "names": ["cache", "createDefaultMetadata", "createDefaultViewport", "resolveOpenGraph", "resolveTwitter", "resolveTitle", "resolveAsArrayOrUndefined", "getComponentTypeModule", "getLayoutOrPageModule", "interopDefault", "resolveAlternates", "resolveAppleWebApp", "resolveAppLinks", "resolveRobots", "resolveThemeColor", "resolveVerification", "resolveItunes", "resolveFacebook", "resolvePagination", "resolveIcons", "getTracer", "ResolveMetadataSpan", "PAGE_SEGMENT_KEY", "Log", "createServerParamsForMetadata", "isFavicon", "icon", "url", "toString", "startsWith", "type", "mergeStaticMetadata", "source", "target", "staticFilesMetadata", "metadataContext", "titleTemplates", "leafSegmentStaticIcons", "pathname", "apple", "openGraph", "twitter", "manifest", "hasOwnProperty", "resolvedTwitter", "images", "metadataBase", "isStaticMetadataRouteFile", "resolvedOpenGraph", "mergeMetadata", "route", "buildState", "key_", "key", "title", "alternates", "facebook", "verification", "icons", "appleWebApp", "appLinks", "robots", "authors", "itunes", "pagination", "other", "Object", "assign", "warnings", "add", "mergeViewport", "themeColor", "colorScheme", "getDefinedViewport", "mod", "props", "tracingProps", "generateViewport", "parent", "trace", "spanName", "attributes", "viewport", "getDefinedMetadata", "generateMetadata", "metadata", "collectStaticImagesFiles", "undefined", "iconPromises", "map", "imageModule", "length", "Promise", "all", "flat", "resolveStaticMetadata", "modules", "staticMetadata", "collectMetadata", "tree", "metadataItems", "errorMetadataItem", "errorConvention", "modType", "hasErrorConventionComponent", "Boolean", "layoutOrPageMod", "layoutOrPageModType", "metadataExport", "push", "errorMod", "errorMetadataExport", "collectViewport", "viewportItems", "errorViewportItemRef", "viewportExport", "errorViewportExport", "current", "resolveMetadataItems", "searchParams", "getDynamicParamFromSegment", "workStore", "parentParams", "treePrefix", "resolveMetadataItemsImpl", "segment", "parallelRoutes", "page", "currentTreePrefix", "isPage", "segmentParam", "currentParams", "value", "param", "params", "layerProps", "filter", "s", "join", "childTree", "keys", "resolveViewportItems", "resolveViewportItemsImpl", "isTitleTruthy", "absolute", "hasTitle", "inheritFromMetadata", "description", "commonOgKeys", "postProcessMetadata", "favicon", "autoFillProps", "hasTwTitle", "hasTwDescription", "hasTwImages", "partialTwitter", "unshift", "prerenderMetadata", "resolversAndResults", "i", "getResult", "prerenderViewport", "exportForResult", "result", "resolve", "catch", "err", "__nextError", "resolvePendingResult", "parentResult", "resolveParentResult", "process", "env", "NODE_ENV", "require", "deepFreeze", "cloneMetadata", "accumulateMetadata", "resolvedMetadata", "Set", "resultIndex", "iconMod", "shift", "pendingMetadata", "resolveParentMetadata", "isPromiseLike", "template", "size", "warning", "warn", "accumulateViewport", "resolvedViewport", "pendingViewport", "resolveParentViewport", "resolveMetadata", "resolveViewport", "then"], "mappings": "AAyBA,6DAA6D;AAC7D,OAAO,cAAa;AAEpB,SAASA,KAAK,QAAQ,QAAO;AAC7B,SACEC,qBAAqB,EACrBC,qBAAqB,QAChB,qBAAoB;AAC3B,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,gCAA+B;AAChF,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,yBAAyB,QAAQ,mBAAkB;AAC5D,SACEC,sBAAsB,EACtBC,qBAAqB,QAChB,kCAAiC;AACxC,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,iBAAiB,QACZ,6BAA4B;AACnC,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,SAAS,QAAQ,gCAA+B;AACzD,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,6BAA6B,QAAQ,8BAA6B;AAqC3E,SAASC,UAAUC,IAAgC;IACjD,IAAI,CAACA,MAAM;QACT,OAAO;IACT;IAEA,yCAAyC;IACzC,OACE,AAACA,CAAAA,KAAKC,GAAG,KAAK,kBACZD,KAAKC,GAAG,CAACC,QAAQ,GAAGC,UAAU,CAAC,gBAAe,KAChDH,KAAKI,IAAI,KAAK;AAElB;AAEA,eAAeC,oBACbC,MAAuB,EACvBC,MAAwB,EACxBC,mBAAmC,EACnCC,eAAgC,EAChCC,cAA8B,EAC9BC,sBAAmC,EACnCC,QAAyB;QAeTN,iBAWEA;IAxBlB,IAAI,CAACE,qBAAqB,OAAOD;IACjC,MAAM,EAAEP,IAAI,EAAEa,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGR;IAEtD,uDAAuD;IAEvD,IAAIR,MAAM;QACRW,uBAAuBX,IAAI,GAAGA;IAChC;IACA,IAAIa,OAAO;QACTF,uBAAuBE,KAAK,GAAGA;IACjC;IAEA,8FAA8F;IAC9F,IAAIE,WAAW,EAACT,2BAAAA,kBAAAA,OAAQS,OAAO,qBAAfT,gBAAiBW,cAAc,CAAC,YAAW;QACzD,MAAMC,kBAAkBxC,eACtB;YAAE,GAAG6B,OAAOQ,OAAO;YAAEI,QAAQJ;QAAQ,GACrCR,OAAOa,YAAY,EACnB;YAAE,GAAGX,eAAe;YAAEY,2BAA2B;QAAK,GACtDX,eAAeK,OAAO;QAExBR,OAAOQ,OAAO,GAAGG;IACnB;IAEA,gGAAgG;IAChG,IAAIJ,aAAa,EAACR,2BAAAA,oBAAAA,OAAQQ,SAAS,qBAAjBR,kBAAmBW,cAAc,CAAC,YAAW;QAC7D,MAAMK,oBAAoB,MAAM7C,iBAC9B;YAAE,GAAG8B,OAAOO,SAAS;YAAEK,QAAQL;QAAU,GACzCP,OAAOa,YAAY,EACnBR,UACA;YAAE,GAAGH,eAAe;YAAEY,2BAA2B;QAAK,GACtDX,eAAeI,SAAS;QAE1BP,OAAOO,SAAS,GAAGQ;IACrB;IACA,IAAIN,UAAU;QACZT,OAAOS,QAAQ,GAAGA;IACpB;IAEA,OAAOT;AACT;AAEA,+DAA+D;AAC/D,eAAegB,cACbC,KAAa,EACbZ,QAAyB,EACzB,EACEN,MAAM,EACNC,MAAM,EACNC,mBAAmB,EACnBE,cAAc,EACdD,eAAe,EACfgB,UAAU,EACVd,sBAAsB,EASvB;IAED,sFAAsF;IACtF,MAAMS,eACJ,QAAOd,0BAAAA,OAAQc,YAAY,MAAK,cAC5Bd,OAAOc,YAAY,GACnBb,OAAOa,YAAY;IACzB,IAAK,MAAMM,QAAQpB,OAAQ;QACzB,MAAMqB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAS;oBACZpB,OAAOqB,KAAK,GAAGjD,aAAa2B,OAAOsB,KAAK,EAAElB,eAAekB,KAAK;oBAC9D;gBACF;YACA,KAAK;gBAAc;oBACjBrB,OAAOsB,UAAU,GAAG,MAAM7C,kBACxBsB,OAAOuB,UAAU,EACjBT,cACAR,UACAH;oBAEF;gBACF;YACA,KAAK;gBAAa;oBAChBF,OAAOO,SAAS,GAAG,MAAMrC,iBACvB6B,OAAOQ,SAAS,EAChBM,cACAR,UACAH,iBACAC,eAAeI,SAAS;oBAE1B;gBACF;YACA,KAAK;gBAAW;oBACdP,OAAOQ,OAAO,GAAGrC,eACf4B,OAAOS,OAAO,EACdK,cACAX,iBACAC,eAAeK,OAAO;oBAExB;gBACF;YACA,KAAK;gBACHR,OAAOuB,QAAQ,GAAGvC,gBAAgBe,OAAOwB,QAAQ;gBACjD;YACF,KAAK;gBACHvB,OAAOwB,YAAY,GAAG1C,oBAAoBiB,OAAOyB,YAAY;gBAC7D;YAEF,KAAK;gBAAS;oBACZxB,OAAOyB,KAAK,GAAGvC,aAAaa,OAAO0B,KAAK;oBACxC;gBACF;YACA,KAAK;gBACHzB,OAAO0B,WAAW,GAAGhD,mBAAmBqB,OAAO2B,WAAW;gBAC1D;YACF,KAAK;gBACH1B,OAAO2B,QAAQ,GAAGhD,gBAAgBoB,OAAO4B,QAAQ;gBACjD;YACF,KAAK;gBAAU;oBACb3B,OAAO4B,MAAM,GAAGhD,cAAcmB,OAAO6B,MAAM;oBAC3C;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY;oBACf5B,MAAM,CAACoB,IAAI,GAAG/C,0BAA0B0B,MAAM,CAACqB,IAAI;oBACnD;gBACF;YACA,KAAK;gBAAW;oBACdpB,MAAM,CAACoB,IAAI,GAAG/C,0BAA0B0B,OAAO8B,OAAO;oBACtD;gBACF;YACA,KAAK;gBAAU;oBACb7B,MAAM,CAACoB,IAAI,GAAG,MAAMrC,cAClBgB,OAAO+B,MAAM,EACbjB,cACAR,UACAH;oBAEF;gBACF;YACA,KAAK;gBAAc;oBACjBF,OAAO+B,UAAU,GAAG,MAAM9C,kBACxBc,OAAOgC,UAAU,EACjBlB,cACAR,UACAH;oBAEF;gBACF;YACA,+CAA+C;YAC/C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qCAAqC;gBACrCF,MAAM,CAACoB,IAAI,GAAGrB,MAAM,CAACqB,IAAI,IAAI;gBAC7B;YACF,KAAK;gBACHpB,OAAOgC,KAAK,GAAGC,OAAOC,MAAM,CAAC,CAAC,GAAGlC,OAAOgC,KAAK,EAAEjC,OAAOiC,KAAK;gBAC3D;YACF,KAAK;gBACHhC,OAAOa,YAAY,GAAGA;gBACtB;YAEF;gBAAS;oBACP,IACE,AAACO,CAAAA,QAAQ,cACPA,QAAQ,gBACRA,QAAQ,aAAY,KACtBrB,MAAM,CAACqB,IAAI,IAAI,MACf;wBACAF,WAAWiB,QAAQ,CAACC,GAAG,CACrB,CAAC,qBAAqB,EAAEhB,IAAI,qCAAqC,EAAEH,MAAM,8HAA8H,CAAC;oBAE5M;oBACA;gBACF;QACF;IACF;IACA,OAAOnB,oBACLC,QACAC,QACAC,qBACAC,iBACAC,gBACAC,wBACAC;AAEJ;AAEA,SAASgC,cAAc,EACrBrC,MAAM,EACND,MAAM,EAIP;IACC,IAAI,CAACA,QAAQ;IACb,IAAK,MAAMoB,QAAQpB,OAAQ;QACzB,MAAMqB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAc;oBACjBpB,OAAOsC,UAAU,GAAGzD,kBAAkBkB,OAAOuC,UAAU;oBACvD;gBACF;YACA,KAAK;gBACHtC,OAAOuC,WAAW,GAAGxC,OAAOwC,WAAW,IAAI;gBAC3C;YACF;gBACE,6CAA6C;gBAC7C,iCAAiC;gBACjCvC,MAAM,CAACoB,IAAI,GAAGrB,MAAM,CAACqB,IAAI;gBACzB;QACJ;IACF;AACF;AAEA,SAASoB,mBACPC,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIG,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAE3B,KAAK,EAAE,GAAG0B;QAClB,OAAO,CAACE,SACN1D,YAAY2D,KAAK,CACf1D,oBAAoBwD,gBAAgB,EACpC;gBACEG,UAAU,CAAC,iBAAiB,EAAE9B,OAAO;gBACrC+B,YAAY;oBACV,aAAa/B;gBACf;YACF,GACA,IAAMwB,IAAIG,gBAAgB,CAACF,OAAOG;IAExC;IACA,OAAOJ,IAAIQ,QAAQ,IAAI;AACzB;AAEA,SAASC,mBACPT,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIU,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAElC,KAAK,EAAE,GAAG0B;QAClB,OAAO,CAACE,SACN1D,YAAY2D,KAAK,CACf1D,oBAAoB+D,gBAAgB,EACpC;gBACEJ,UAAU,CAAC,iBAAiB,EAAE9B,OAAO;gBACrC+B,YAAY;oBACV,aAAa/B;gBACf;YACF,GACA,IAAMwB,IAAIU,gBAAgB,CAACT,OAAOG;IAExC;IACA,OAAOJ,IAAIW,QAAQ,IAAI;AACzB;AAEA,eAAeC,yBACbD,QAAmC,EACnCV,KAAU,EACV7C,IAAkD;QAU7C;IARL,IAAI,EAACuD,4BAAAA,QAAU,CAACvD,KAAK,GAAE,OAAOyD;IAE9B,MAAMC,eAAeH,QAAQ,CAACvD,KAAyB,CAAC2D,GAAG,CACzD,OAAOC,cACLjF,eAAe,MAAMiF,YAAYf;IAGrC,OAAOa,CAAAA,gCAAAA,aAAcG,MAAM,IAAG,KACzB,QAAA,MAAMC,QAAQC,GAAG,CAACL,kCAAnB,AAAC,MAAkCM,IAAI,KACvCP;AACN;AAEA,eAAeQ,sBACbC,OAAsB,EACtBrB,KAAU;IAEV,MAAM,EAAEU,QAAQ,EAAE,GAAGW;IACrB,IAAI,CAACX,UAAU,OAAO;IAEtB,MAAM,CAAC3D,MAAMa,OAAOC,WAAWC,QAAQ,GAAG,MAAMmD,QAAQC,GAAG,CAAC;QAC1DP,yBAAyBD,UAAUV,OAAO;QAC1CW,yBAAyBD,UAAUV,OAAO;QAC1CW,yBAAyBD,UAAUV,OAAO;QAC1CW,yBAAyBD,UAAUV,OAAO;KAC3C;IAED,MAAMsB,iBAAiB;QACrBvE;QACAa;QACAC;QACAC;QACAC,UAAU2C,SAAS3C,QAAQ;IAC7B;IAEA,OAAOuD;AACT;AAEA,4FAA4F;AAC5F,eAAeC,gBAAgB,EAC7BC,IAAI,EACJC,aAAa,EACbC,iBAAiB,EACjB1B,KAAK,EACLzB,KAAK,EACLoD,eAAe,EAQhB;IACC,IAAI5B;IACJ,IAAI6B;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnB5B,MAAM,MAAMnE,uBAAuB4F,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACL,MAAM,EAAE5B,KAAKgC,eAAe,EAAEH,SAASI,mBAAmB,EAAE,GAC1D,MAAMnG,sBAAsB2F;QAC9BzB,MAAMgC;QACNH,UAAUI;IACZ;IAEA,IAAIJ,SAAS;QACXrD,SAAS,CAAC,CAAC,EAAEqD,SAAS;IACxB;IAEA,MAAMrE,sBAAsB,MAAM6D,sBAAsBI,IAAI,CAAC,EAAE,EAAExB;IACjE,MAAMiC,iBAAiBlC,MAAMS,mBAAmBT,KAAKC,OAAO;QAAEzB;IAAM,KAAK;IAEzEkD,cAAcS,IAAI,CAAC;QAACD;QAAgB1E;KAAoB;IAExD,IAAIsE,+BAA+BF,iBAAiB;QAClD,MAAMQ,WAAW,MAAMvG,uBAAuB4F,MAAMG;QACpD,MAAMS,sBAAsBD,WACxB3B,mBAAmB2B,UAAUnC,OAAO;YAAEzB;QAAM,KAC5C;QAEJmD,iBAAiB,CAAC,EAAE,GAAGU;QACvBV,iBAAiB,CAAC,EAAE,GAAGnE;IACzB;AACF;AAEA,4FAA4F;AAC5F,eAAe8E,gBAAgB,EAC7Bb,IAAI,EACJc,aAAa,EACbC,oBAAoB,EACpBvC,KAAK,EACLzB,KAAK,EACLoD,eAAe,EAQhB;IACC,IAAI5B;IACJ,IAAI6B;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnB5B,MAAM,MAAMnE,uBAAuB4F,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACL,MAAM,EAAE5B,KAAKgC,eAAe,EAAEH,SAASI,mBAAmB,EAAE,GAC1D,MAAMnG,sBAAsB2F;QAC9BzB,MAAMgC;QACNH,UAAUI;IACZ;IAEA,IAAIJ,SAAS;QACXrD,SAAS,CAAC,CAAC,EAAEqD,SAAS;IACxB;IAEA,MAAMY,iBAAiBzC,MAAMD,mBAAmBC,KAAKC,OAAO;QAAEzB;IAAM,KAAK;IAEzE+D,cAAcJ,IAAI,CAACM;IAEnB,IAAIX,+BAA+BF,iBAAiB;QAClD,MAAMQ,WAAW,MAAMvG,uBAAuB4F,MAAMG;QACpD,MAAMc,sBAAsBN,WACxBrC,mBAAmBqC,UAAUnC,OAAO;YAAEzB;QAAM,KAC5C;QAEJgE,qBAAqBG,OAAO,GAAGD;IACjC;AACF;AAEA,MAAME,uBAAuBtH,MAAM,eACjCmG,IAAgB,EAChBoB,YAAqC,EACrCjB,eAA8C,EAC9CkB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAMC,eAAe,CAAC;IACtB,MAAMtB,gBAA+B,EAAE;IACvC,MAAMC,oBAA2C;QAAC;QAAM;KAAK;IAC7D,MAAMsB,aAAapC;IACnB,OAAOqC,yBACLxB,eACAD,MACAwB,YACAD,cACAH,cACAjB,iBACAD,mBACAmB,4BACAC;AAEJ;AAEA,eAAeG,yBACbxB,aAA4B,EAC5BD,IAAgB,EAChB,6FAA6F,GAC7FwB,UAAgC,EAChCD,YAAoB,EACpBH,YAAqC,EACrCjB,eAA8C,EAC9CD,iBAAwC,EACxCmB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAM,CAACI,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAG5B;IAC5C,MAAM6B,oBACJL,cAAcA,WAAWhC,MAAM,GAAG;WAAIgC;QAAYE;KAAQ,GAAG;QAACA;KAAQ;IACxE,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeV,2BAA2BK;IAChD;;GAEC,GACD,IAAIM,gBAAgBT;IACpB,IAAIQ,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGT,YAAY;YACf,CAACQ,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAME,SAAS9G,8BAA8B2G,eAAeV;IAE5D,IAAIc;IACJ,IAAIN,QAAQ;QACVM,aAAa;YACXD;YACAf;QACF;IACF,OAAO;QACLgB,aAAa;YACXD;QACF;IACF;IAEA,MAAMpC,gBAAgB;QACpBC;QACAC;QACAC;QACAC;QACA3B,OAAO4D;QACPrF,OAAO8E,iBACL,yCAAyC;SACxCQ,MAAM,CAAC,CAACC,IAAMA,MAAMnH,kBACpBoH,IAAI,CAAC;IACV;IAEA,IAAK,MAAMrF,OAAOyE,eAAgB;QAChC,MAAMa,YAAYb,cAAc,CAACzE,IAAI;QACrC,MAAMuE,yBACJxB,eACAuC,WACAX,mBACAG,eACAZ,cACAjB,iBACAD,mBACAmB,4BACAC;IAEJ;IAEA,IAAIvD,OAAO0E,IAAI,CAACd,gBAAgBnC,MAAM,KAAK,KAAKW,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCF,cAAcS,IAAI,CAACR;IACrB;IAEA,OAAOD;AACT;AAGA,MAAMyC,uBAAuB7I,MAAM,eACjCmG,IAAgB,EAChBoB,YAAqC,EACrCjB,eAA8C,EAC9CkB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAMC,eAAe,CAAC;IACtB,MAAMT,gBAA+B,EAAE;IACvC,MAAMC,uBAA6C;QACjDG,SAAS;IACX;IACA,MAAMM,aAAapC;IACnB,OAAOuD,yBACL7B,eACAd,MACAwB,YACAD,cACAH,cACAjB,iBACAY,sBACAM,4BACAC;AAEJ;AAEA,eAAeqB,yBACb7B,aAA4B,EAC5Bd,IAAgB,EAChB,6FAA6F,GAC7FwB,UAAgC,EAChCD,YAAoB,EACpBH,YAAqC,EACrCjB,eAA8C,EAC9CY,oBAA0C,EAC1CM,0BAAsD,EACtDC,SAAoB;IAEpB,MAAM,CAACI,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAG5B;IAC5C,MAAM6B,oBACJL,cAAcA,WAAWhC,MAAM,GAAG;WAAIgC;QAAYE;KAAQ,GAAG;QAACA;KAAQ;IACxE,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeV,2BAA2BK;IAChD;;GAEC,GACD,IAAIM,gBAAgBT;IACpB,IAAIQ,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGT,YAAY;YACf,CAACQ,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAME,SAAS9G,8BAA8B2G,eAAeV;IAE5D,IAAIc;IACJ,IAAIN,QAAQ;QACVM,aAAa;YACXD;YACAf;QACF;IACF,OAAO;QACLgB,aAAa;YACXD;QACF;IACF;IAEA,MAAMtB,gBAAgB;QACpBb;QACAc;QACAC;QACAZ;QACA3B,OAAO4D;QACPrF,OAAO8E,iBACL,yCAAyC;SACxCQ,MAAM,CAAC,CAACC,IAAMA,MAAMnH,kBACpBoH,IAAI,CAAC;IACV;IAEA,IAAK,MAAMrF,OAAOyE,eAAgB;QAChC,MAAMa,YAAYb,cAAc,CAACzE,IAAI;QACrC,MAAMyF,yBACJ7B,eACA0B,WACAX,mBACAG,eACAZ,cACAjB,iBACAY,sBACAM,4BACAC;IAEJ;IAEA,IAAIvD,OAAO0E,IAAI,CAACd,gBAAgBnC,MAAM,KAAK,KAAKW,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCW,cAAcJ,IAAI,CAACK,qBAAqBG,OAAO;IACjD;IAEA,OAAOJ;AACT;AAKA,MAAM8B,gBAAgB,CAACzF,QACrB,CAAC,EAACA,yBAAAA,MAAO0F,QAAQ;AACnB,MAAMC,WAAW,CAAC5D,WAA+B0D,cAAc1D,4BAAAA,SAAU/B,KAAK;AAE9E,SAAS4F,oBACPjH,MAA4C,EAC5CoD,QAA0B;IAE1B,IAAIpD,QAAQ;QACV,IAAI,CAACgH,SAAShH,WAAWgH,SAAS5D,WAAW;YAC3CpD,OAAOqB,KAAK,GAAG+B,SAAS/B,KAAK;QAC/B;QACA,IAAI,CAACrB,OAAOkH,WAAW,IAAI9D,SAAS8D,WAAW,EAAE;YAC/ClH,OAAOkH,WAAW,GAAG9D,SAAS8D,WAAW;QAC3C;IACF;AACF;AAEA,6DAA6D;AAC7D,MAAMC,eAAe;IAAC;IAAS;IAAe;CAAS;AACvD,SAASC,oBACPhE,QAA0B,EAC1BiE,OAAY,EACZlH,cAA8B,EAC9BD,eAAgC;IAEhC,MAAM,EAAEK,SAAS,EAAEC,OAAO,EAAE,GAAG4C;IAE/B,IAAI7C,WAAW;QACb,kEAAkE;QAClE,wCAAwC;QACxC,IAAI+G,gBAIC,CAAC;QACN,MAAMC,aAAaP,SAASxG;QAC5B,MAAMgH,mBAAmBhH,2BAAAA,QAAS0G,WAAW;QAC7C,MAAMO,cAAcjD,QAClBhE,CAAAA,2BAAAA,QAASE,cAAc,CAAC,cAAaF,QAAQI,MAAM;QAErD,IAAI,CAAC2G,YAAY;YACf,IAAIT,cAAcvG,UAAUc,KAAK,GAAG;gBAClCiG,cAAcjG,KAAK,GAAGd,UAAUc,KAAK;YACvC,OAAO,IAAI+B,SAAS/B,KAAK,IAAIyF,cAAc1D,SAAS/B,KAAK,GAAG;gBAC1DiG,cAAcjG,KAAK,GAAG+B,SAAS/B,KAAK;YACtC;QACF;QACA,IAAI,CAACmG,kBACHF,cAAcJ,WAAW,GACvB3G,UAAU2G,WAAW,IAAI9D,SAAS8D,WAAW,IAAI5D;QACrD,IAAI,CAACmE,aAAaH,cAAc1G,MAAM,GAAGL,UAAUK,MAAM;QAEzD,IAAIqB,OAAO0E,IAAI,CAACW,eAAe5D,MAAM,GAAG,GAAG;YACzC,MAAMgE,iBAAiBvJ,eACrBmJ,eACAlE,SAASvC,YAAY,EACrBX,iBACAC,eAAeK,OAAO;YAExB,IAAI4C,SAAS5C,OAAO,EAAE;gBACpB4C,SAAS5C,OAAO,GAAGyB,OAAOC,MAAM,CAAC,CAAC,GAAGkB,SAAS5C,OAAO,EAAE;oBACrD,GAAI,CAAC+G,cAAc;wBAAElG,KAAK,EAAEqG,kCAAAA,eAAgBrG,KAAK;oBAAC,CAAC;oBACnD,GAAI,CAACmG,oBAAoB;wBACvBN,WAAW,EAAEQ,kCAAAA,eAAgBR,WAAW;oBAC1C,CAAC;oBACD,GAAI,CAACO,eAAe;wBAAE7G,MAAM,EAAE8G,kCAAAA,eAAgB9G,MAAM;oBAAC,CAAC;gBACxD;YACF,OAAO;gBACLwC,SAAS5C,OAAO,GAAGkH;YACrB;QACF;IACF;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/CT,oBAAoB1G,WAAW6C;IAC/B6D,oBAAoBzG,SAAS4C;IAE7B,IAAIiE,SAAS;QACX,IAAI,CAACjE,SAAS3B,KAAK,EAAE;YACnB2B,SAAS3B,KAAK,GAAG;gBACfhC,MAAM,EAAE;gBACRa,OAAO,EAAE;YACX;QACF;QAEA8C,SAAS3B,KAAK,CAAChC,IAAI,CAACkI,OAAO,CAACN;IAC9B;IAEA,OAAOjE;AACT;AAIA,SAASwE,kBAAkBzD,aAA4B;IACrD,qEAAqE;IACrE,+EAA+E;IAC/E,UAAU;IACV,MAAM0D,sBAEF,EAAE;IACN,IAAK,IAAIC,IAAI,GAAGA,IAAI3D,cAAcT,MAAM,EAAEoE,IAAK;QAC7C,MAAMnD,iBAAiBR,aAAa,CAAC2D,EAAE,CAAC,EAAE;QAC1CC,UAAUF,qBAAqBlD;IACjC;IACA,OAAOkD;AACT;AAEA,SAASG,kBAAkBhD,aAA4B;IACrD,qEAAqE;IACrE,+EAA+E;IAC/E,UAAU;IACV,MAAM6C,sBAEF,EAAE;IACN,IAAK,IAAIC,IAAI,GAAGA,IAAI9C,cAActB,MAAM,EAAEoE,IAAK;QAC7C,MAAM5C,iBAAiBF,aAAa,CAAC8C,EAAE;QACvCC,UAAUF,qBAAqB3C;IACjC;IACA,OAAO2C;AACT;AAIA,SAASE,UACPF,mBAAsE,EACtEI,eAAyE;IAEzE,IAAI,OAAOA,oBAAoB,YAAY;QACzC,MAAMC,SAASD,gBACb,IAAItE,QAAqB,CAACwE,UAAYN,oBAAoBjD,IAAI,CAACuD;QAEjEN,oBAAoBjD,IAAI,CAACsD;QACzB,IAAIA,kBAAkBvE,SAAS;YAC7B,8CAA8C;YAC9C,+CAA+C;YAC/C,4CAA4C;YAC5C,oDAAoD;YACpDuE,OAAOE,KAAK,CAAC,CAACC;gBACZ,OAAO;oBACLC,aAAaD;gBACf;YACF;QACF;IACF,OAAO,IAAI,OAAOJ,oBAAoB,UAAU;QAC9CJ,oBAAoBjD,IAAI,CAACqD;IAC3B,OAAO;QACLJ,oBAAoBjD,IAAI,CAAC;IAC3B;AACF;AAEA,SAAS2D,qBAGPC,YAA0B,EAC1BC,mBAAkD;IAElD,uFAAuF;IACvF,qEAAqE;IACrE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,2EAA2E;QAC3E,4DAA4D;QAC5DJ,eAAe,AACbK,QAAQ,gCACRC,UAAU,CACV,AACED,QAAQ,oBACRE,aAAa,CAACP;IAEpB;IAEAC,oBAAoBD;AACtB;AAEA,OAAO,eAAeQ,mBACpB/H,KAAa,EACbkD,aAA4B,EAC5B9D,QAAyB,EACzBH,eAAgC;IAEhC,IAAI+I,mBAAmBjL;IAEvB,IAAImC,iBAAiC;QACnCkB,OAAO;QACPb,SAAS;QACTD,WAAW;IACb;IAEA,MAAMW,aAAa;QACjBiB,UAAU,IAAI+G;IAChB;IAEA,IAAI7B;IAEJ,kDAAkD;IAClD,+EAA+E;IAC/E,MAAMjH,yBAAyB;QAC7BX,MAAM,EAAE;QACRa,OAAO,EAAE;IACX;IAEA,MAAMuH,sBAAsBD,kBAAkBzD;IAC9C,IAAIgF,cAAc;IAElB,IAAK,IAAIrB,IAAI,GAAGA,IAAI3D,cAAcT,MAAM,EAAEoE,IAAK;YAIrB7H;QAHxB,MAAMA,sBAAsBkE,aAAa,CAAC2D,EAAE,CAAC,EAAE;QAC/C,yEAAyE;QACzE,qEAAqE;QACrE,IAAIA,KAAK,KAAKtI,UAAUS,wCAAAA,4BAAAA,oBAAqBR,IAAI,qBAAzBQ,yBAA2B,CAAC,EAAE,GAAG;gBACvCA;YAAhB,MAAMmJ,UAAUnJ,wCAAAA,6BAAAA,oBAAqBR,IAAI,qBAAzBQ,2BAA2BoJ,KAAK;YAChD,IAAIvB,MAAM,GAAGT,UAAU+B;QACzB;QAEA,IAAIE,kBAAkBzB,mBAAmB,CAACsB,cAAc;QACxD,IAAI,OAAOG,oBAAoB,YAAY;YACzC,kDAAkD;YAClD,qDAAqD;YACrD,4BAA4B;YAC5B,MAAMC,wBAAwBD;YAC9B,sDAAsD;YACtD,iBAAiB;YACjBA,kBAAkBzB,mBAAmB,CAACsB,cAAc;YAEpDZ,qBAAqBU,kBAAkBM;QACzC;QACA,wDAAwD;QAExD,IAAInG;QACJ,IAAIoG,cAAcF,kBAAkB;YAClClG,WAAW,MAAMkG;QACnB,OAAO;YACLlG,WAAWkG;QACb;QAEAL,mBAAmB,MAAMjI,cAAcC,OAAOZ,UAAU;YACtDL,QAAQiJ;YACRlJ,QAAQqD;YACRlD;YACAD;YACAE;YACAe;YACAd;QACF;QAEA,gFAAgF;QAChF,kDAAkD;QAClD,IAAI0H,IAAI3D,cAAcT,MAAM,GAAG,GAAG;gBAEvBuF,yBACIA,6BACFA;YAHX9I,iBAAiB;gBACfkB,OAAO4H,EAAAA,0BAAAA,iBAAiB5H,KAAK,qBAAtB4H,wBAAwBQ,QAAQ,KAAI;gBAC3ClJ,WAAW0I,EAAAA,8BAAAA,iBAAiB1I,SAAS,qBAA1B0I,4BAA4B5H,KAAK,CAACoI,QAAQ,KAAI;gBACzDjJ,SAASyI,EAAAA,4BAAAA,iBAAiBzI,OAAO,qBAAxByI,0BAA0B5H,KAAK,CAACoI,QAAQ,KAAI;YACvD;QACF;IACF;IAEA,IACErJ,uBAAuBX,IAAI,CAACiE,MAAM,GAAG,KACrCtD,uBAAuBE,KAAK,CAACoD,MAAM,GAAG,GACtC;QACA,IAAI,CAACuF,iBAAiBxH,KAAK,EAAE;YAC3BwH,iBAAiBxH,KAAK,GAAG;gBACvBhC,MAAM,EAAE;gBACRa,OAAO,EAAE;YACX;YACA,IAAIF,uBAAuBX,IAAI,CAACiE,MAAM,GAAG,GAAG;gBAC1CuF,iBAAiBxH,KAAK,CAAChC,IAAI,CAACkI,OAAO,IAAIvH,uBAAuBX,IAAI;YACpE;YACA,IAAIW,uBAAuBE,KAAK,CAACoD,MAAM,GAAG,GAAG;gBAC3CuF,iBAAiBxH,KAAK,CAACnB,KAAK,CAACqH,OAAO,IAAIvH,uBAAuBE,KAAK;YACtE;QACF;IACF;IAEA,qGAAqG;IACrG,IAAIY,WAAWiB,QAAQ,CAACuH,IAAI,GAAG,GAAG;QAChC,KAAK,MAAMC,WAAWzI,WAAWiB,QAAQ,CAAE;YACzC7C,IAAIsK,IAAI,CAACD;QACX;IACF;IAEA,OAAOvC,oBACL6B,kBACA5B,SACAlH,gBACAD;AAEJ;AAEA,OAAO,eAAe2J,mBACpB7E,aAA4B;IAE5B,MAAM8E,mBAAqC7L;IAE3C,MAAM4J,sBAAsBG,kBAAkBhD;IAC9C,IAAI8C,IAAI;IAER,MAAOA,IAAID,oBAAoBnE,MAAM,CAAE;QACrC,IAAIqG,kBAAkBlC,mBAAmB,CAACC,IAAI;QAC9C,IAAI,OAAOiC,oBAAoB,YAAY;YACzC,kDAAkD;YAClD,qDAAqD;YACrD,4BAA4B;YAC5B,MAAMC,wBAAwBD;YAC9B,sDAAsD;YACtD,iBAAiB;YACjBA,kBAAkBlC,mBAAmB,CAACC,IAAI;YAE1CS,qBAAqBuB,kBAAkBE;QACzC;QACA,wDAAwD;QAExD,IAAI/G;QACJ,IAAIuG,cAAcO,kBAAkB;YAClC9G,WAAW,MAAM8G;QACnB,OAAO;YACL9G,WAAW8G;QACb;QAEA1H,cAAc;YACZrC,QAAQ8J;YACR/J,QAAQkD;QACV;IACF;IACA,OAAO6G;AACT;AAEA,sHAAsH;AACtH,OAAO,eAAeG,gBACpB/F,IAAgB,EAChB7D,QAAyB,EACzBiF,YAAqC,EACrCjB,eAA8C,EAC9CkB,0BAAsD,EACtDC,SAAoB,EACpBtF,eAAgC;IAEhC,MAAMiE,gBAAgB,MAAMkB,qBAC1BnB,MACAoB,cACAjB,iBACAkB,4BACAC;IAEF,OAAOwD,mBACLxD,UAAUvE,KAAK,EACfkD,eACA9D,UACAH;AAEJ;AAEA,sHAAsH;AACtH,OAAO,eAAegK,gBACpBhG,IAAgB,EAChBoB,YAAqC,EACrCjB,eAA8C,EAC9CkB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAMR,gBAAgB,MAAM4B,qBAC1B1C,MACAoB,cACAjB,iBACAkB,4BACAC;IAEF,OAAOqE,mBAAmB7E;AAC5B;AAEA,SAASwE,cACPrD,KAA+B;IAE/B,OACE,OAAOA,UAAU,YACjBA,UAAU,QACV,OAAO,AAACA,MAA+BgE,IAAI,KAAK;AAEpD", "ignoreList": [0]}
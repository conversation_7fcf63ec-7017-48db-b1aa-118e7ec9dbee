{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-dom.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactDOM\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM"], "mappings": ";AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,QAAQ", "ignoreList": [0]}
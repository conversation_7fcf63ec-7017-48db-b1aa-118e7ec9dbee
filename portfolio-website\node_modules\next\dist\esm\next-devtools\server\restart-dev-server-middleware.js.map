{"version": 3, "sources": ["../../../src/next-devtools/server/restart-dev-server-middleware.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport type { Telemetry } from '../../telemetry/storage'\nimport { RESTART_EXIT_CODE } from '../../server/lib/utils'\nimport { middlewareResponse } from './middleware-response'\nimport type { Project } from '../../build/swc/types'\nimport { invalidatePersistentCache as invalidateWebpackPersistentCache } from '../../build/webpack/cache-invalidation'\n\nconst EVENT_DEV_OVERLAY_RESTART_SERVER = 'DEV_OVERLAY_RESTART_SERVER'\n\ninterface RestartDevServerMiddlewareConfig {\n  telemetry: Telemetry\n  turbopackProject?: Project\n  webpackCacheDirectories?: Set<string>\n}\n\nexport function getRestartDevServerMiddleware({\n  telemetry,\n  turbopackProject,\n  webpackCacheDirectories,\n}: RestartDevServerMiddlewareConfig) {\n  /**\n   * Some random value between 1 and Number.MAX_SAFE_INTEGER (inclusive). The same value is returned\n   * on every call to `__nextjs_server_status` until the server is restarted.\n   *\n   * Can be used to determine if two server status responses are from the same process or a\n   * different (restarted) process.\n   */\n  const executionId: number =\n    Math.floor(Math.random() * Number.MAX_SAFE_INTEGER) + 1\n\n  async function handleRestartRequest(\n    req: IncomingMessage,\n    res: ServerResponse,\n    searchParams: URLSearchParams\n  ) {\n    if (req.method !== 'POST') {\n      return middlewareResponse.methodNotAllowed(res)\n    }\n\n    const shouldInvalidatePersistentCache = searchParams.has(\n      'invalidatePersistentCache'\n    )\n    if (shouldInvalidatePersistentCache) {\n      if (webpackCacheDirectories != null) {\n        await Promise.all(\n          Array.from(webpackCacheDirectories).map(\n            invalidateWebpackPersistentCache\n          )\n        )\n      }\n      if (turbopackProject != null) {\n        await turbopackProject.invalidatePersistentCache()\n      }\n    }\n\n    telemetry.record({\n      eventName: EVENT_DEV_OVERLAY_RESTART_SERVER,\n      payload: { invalidatePersistentCache: shouldInvalidatePersistentCache },\n    })\n\n    // TODO: Use flushDetached\n    await telemetry.flush()\n\n    // do this async to try to give the response a chance to send\n    // it's not really important if it doesn't though\n    setTimeout(() => {\n      process.exit(RESTART_EXIT_CODE)\n    }, 0)\n\n    return middlewareResponse.noContent(res)\n  }\n\n  async function handleServerStatus(req: IncomingMessage, res: ServerResponse) {\n    if (req.method !== 'GET') {\n      return middlewareResponse.methodNotAllowed(res)\n    }\n\n    return middlewareResponse.json(res, {\n      executionId,\n    })\n  }\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    switch (pathname) {\n      case '/__nextjs_restart_dev':\n        return await handleRestartRequest(req, res, searchParams)\n      case '/__nextjs_server_status':\n        return await handleServerStatus(req, res)\n      default:\n        return next()\n    }\n  }\n}\n"], "names": ["RESTART_EXIT_CODE", "middlewareResponse", "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidateWebpackPersistentCache", "EVENT_DEV_OVERLAY_RESTART_SERVER", "getRestartDevServerMiddleware", "telemetry", "turbopackProject", "webpackCacheDirectories", "executionId", "Math", "floor", "random", "Number", "MAX_SAFE_INTEGER", "handleRestartRequest", "req", "res", "searchParams", "method", "methodNotAllowed", "shouldInvalidatePersistentCache", "has", "Promise", "all", "Array", "from", "map", "record", "eventName", "payload", "flush", "setTimeout", "process", "exit", "noContent", "handleServerStatus", "json", "next", "pathname", "URL", "url"], "mappings": "AAEA,SAASA,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,kBAAkB,QAAQ,wBAAuB;AAE1D,SAASC,6BAA6BC,gCAAgC,QAAQ,yCAAwC;AAEtH,MAAMC,mCAAmC;AAQzC,OAAO,SAASC,8BAA8B,KAIX;IAJW,IAAA,EAC5CC,SAAS,EACTC,gBAAgB,EAChBC,uBAAuB,EACU,GAJW;IAK5C;;;;;;GAMC,GACD,MAAMC,cACJC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAKC,OAAOC,gBAAgB,IAAI;IAExD,eAAeC,qBACbC,GAAoB,EACpBC,GAAmB,EACnBC,YAA6B;QAE7B,IAAIF,IAAIG,MAAM,KAAK,QAAQ;YACzB,OAAOlB,mBAAmBmB,gBAAgB,CAACH;QAC7C;QAEA,MAAMI,kCAAkCH,aAAaI,GAAG,CACtD;QAEF,IAAID,iCAAiC;YACnC,IAAIb,2BAA2B,MAAM;gBACnC,MAAMe,QAAQC,GAAG,CACfC,MAAMC,IAAI,CAAClB,yBAAyBmB,GAAG,CACrCxB;YAGN;YACA,IAAII,oBAAoB,MAAM;gBAC5B,MAAMA,iBAAiBL,yBAAyB;YAClD;QACF;QAEAI,UAAUsB,MAAM,CAAC;YACfC,WAAWzB;YACX0B,SAAS;gBAAE5B,2BAA2BmB;YAAgC;QACxE;QAEA,0BAA0B;QAC1B,MAAMf,UAAUyB,KAAK;QAErB,6DAA6D;QAC7D,iDAAiD;QACjDC,WAAW;YACTC,QAAQC,IAAI,CAAClC;QACf,GAAG;QAEH,OAAOC,mBAAmBkC,SAAS,CAAClB;IACtC;IAEA,eAAemB,mBAAmBpB,GAAoB,EAAEC,GAAmB;QACzE,IAAID,IAAIG,MAAM,KAAK,OAAO;YACxB,OAAOlB,mBAAmBmB,gBAAgB,CAACH;QAC7C;QAEA,OAAOhB,mBAAmBoC,IAAI,CAACpB,KAAK;YAClCR;QACF;IACF;IAEA,OAAO,eACLO,GAAoB,EACpBC,GAAmB,EACnBqB,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAErB,YAAY,EAAE,GAAG,IAAIsB,IAAI,AAAC,aAAUxB,IAAIyB,GAAG;QAE7D,OAAQF;YACN,KAAK;gBACH,OAAO,MAAMxB,qBAAqBC,KAAKC,KAAKC;YAC9C,KAAK;gBACH,OAAO,MAAMkB,mBAAmBpB,KAAKC;YACvC;gBACE,OAAOqB;QACX;IACF;AACF", "ignoreList": [0]}
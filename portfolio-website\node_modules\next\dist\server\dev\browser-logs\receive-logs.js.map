{"version": 3, "sources": ["../../../../src/server/dev/browser-logs/receive-logs.ts"], "sourcesContent": ["import { cyan, dim, red, yellow } from '../../../lib/picocolors'\nimport type { Project } from '../../../build/swc/types'\nimport util from 'util'\nimport {\n  getConsoleLocation,\n  getSourceMappedStackFrames,\n  withLocation,\n  type MappingContext,\n} from './source-map'\nimport {\n  type ServerLogEntry,\n  type LogMethod,\n  type ConsoleEntry,\n  UNDEFINED_MARKER,\n} from '../../../next-devtools/shared/forward-logs-shared'\n\nexport function restoreUndefined(x: any): any {\n  if (x === UNDEFINED_MARKER) return undefined\n  if (Array.isArray(x)) return x.map(restoreUndefined)\n  if (x && typeof x === 'object') {\n    for (let k in x) {\n      x[k] = restoreUndefined(x[k])\n    }\n  }\n  return x\n}\n\nconst methods: Array<LogMethod> = [\n  'log',\n  'info',\n  'warn',\n  'debug',\n  'table',\n  'error',\n  'assert',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n]\n\nconst methodsToSkipInspect = new Set([\n  'table',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n])\n\n// we aren't overriding console, we're just making a (slightly convoluted) helper for replaying user console methods\nconst forwardConsole: typeof console = {\n  ...console,\n  ...Object.fromEntries(\n    methods.map((method) => [\n      method,\n      (...args: Array<any>) =>\n        (console[method] as any)(\n          ...args.map((arg) =>\n            methodsToSkipInspect.has(method) ||\n            typeof arg !== 'object' ||\n            arg === null\n              ? arg\n              : // we hardcode depth:Infinity to allow the true depth to be configured by the serialization done in the browser (which is controlled by user)\n                util.inspect(arg, { depth: Infinity, colors: true })\n          )\n        ),\n    ])\n  ),\n}\n\nasync function deserializeArgData(arg: any) {\n  try {\n    // we want undefined to be represented as it would be in the browser from the user's perspective (otherwise it would be stripped away/shown as null)\n    if (arg === UNDEFINED_MARKER) {\n      return restoreUndefined(arg)\n    }\n\n    return restoreUndefined(JSON.parse(arg))\n  } catch {\n    return arg\n  }\n}\n\nconst colorError = (\n  mapped: Awaited<ReturnType<typeof getSourceMappedStackFrames>>,\n  config?: {\n    prefix?: string\n    applyColor?: boolean\n  }\n) => {\n  const colorFn =\n    config?.applyColor === undefined || config.applyColor ? red : <T>(x: T) => x\n  switch (mapped.kind) {\n    case 'mapped-stack':\n    case 'stack': {\n      return (\n        (config?.prefix ? colorFn(config?.prefix) : '') +\n        `\\n${colorFn(mapped.stack)}`\n      )\n    }\n    case 'with-frame-code': {\n      return (\n        (config?.prefix ? colorFn(config?.prefix) : '') +\n        `\\n${colorFn(mapped.stack)}\\n${mapped.frameCode}`\n      )\n    }\n    // a more sophisticated version of this allows the user to config if they want ignored frames (but we need to be sure to source map them)\n    case 'all-ignored': {\n      return config?.prefix ? colorFn(config?.prefix) : ''\n    }\n    default: {\n    }\n  }\n  mapped satisfies never\n}\n\nfunction processConsoleFormatStrings(args: any[]): any[] {\n  /**\n   * this handles the case formatting is applied to the console log\n   * otherwise we will see the format specifier directly in the terminal output\n   */\n  if (args.length > 0 && typeof args[0] === 'string') {\n    const formatString = args[0]\n    if (\n      formatString.includes('%s') ||\n      formatString.includes('%d') ||\n      formatString.includes('%i') ||\n      formatString.includes('%f') ||\n      formatString.includes('%o') ||\n      formatString.includes('%O') ||\n      formatString.includes('%c')\n    ) {\n      try {\n        const formatted = util.format(...args)\n        return [formatted]\n      } catch {\n        return args\n      }\n    }\n  }\n  return args\n}\n\n// in the case of logging errors, we want to strip formatting\n// modifiers since we apply our own custom coloring to error\n// stacks and code blocks, and otherwise it would conflict\n// and cause awful output\nexport function stripFormatSpecifiers(args: any[]): any[] {\n  if (args.length === 0 || typeof args[0] !== 'string') return args\n\n  const fmtIn = String(args[0])\n  const rest = args.slice(1)\n\n  if (!fmtIn.includes('%')) return args\n\n  let fmtOut = ''\n  let argPtr = 0\n\n  for (let i = 0; i < fmtIn.length; i++) {\n    if (fmtIn[i] !== '%') {\n      fmtOut += fmtIn[i]\n      continue\n    }\n\n    if (fmtIn[i + 1] === '%') {\n      fmtOut += '%'\n      i++\n      continue\n    }\n\n    const token = fmtIn[++i]\n\n    if (!token) {\n      fmtOut += '%'\n      continue\n    }\n\n    if ('csdifoOj'.includes(token) || token === 'O') {\n      if (argPtr < rest.length) {\n        if (token === 'c') {\n          argPtr++\n        } else if (token === 'o' || token === 'O' || token === 'j') {\n          const obj = rest[argPtr++]\n          fmtOut += util.inspect(obj, { depth: 2, colors: false })\n        } else {\n          // string(...) is safe for remaining specifiers\n          fmtOut += String(rest[argPtr++])\n        }\n      }\n      continue\n    }\n\n    fmtOut += '%' + token\n  }\n\n  const result = [fmtOut]\n  if (argPtr < rest.length) {\n    result.push(...rest.slice(argPtr))\n  }\n\n  return result\n}\n\nasync function prepareFormattedErrorArgs(\n  entry: Extract<ServerLogEntry, { kind: 'formatted-error' }>,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const mapped = await getSourceMappedStackFrames(entry.stack, ctx, distDir)\n  return [colorError(mapped, { prefix: entry.prefix })]\n}\n\nasync function prepareConsoleArgs(\n  entry: Extract<ServerLogEntry, { kind: 'console' }>,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserialized = await Promise.all(\n    entry.args.map(async (arg) => {\n      if (arg.kind === 'arg') {\n        const data = await deserializeArgData(arg.data)\n        if (entry.method === 'warn' && typeof data === 'string') {\n          return yellow(data)\n        }\n        return data\n      }\n      if (!arg.stack) return red(arg.prefix)\n      const mapped = await getSourceMappedStackFrames(arg.stack, ctx, distDir)\n      return colorError(mapped, { prefix: arg.prefix, applyColor: false })\n    })\n  )\n\n  return processConsoleFormatStrings(deserialized)\n}\n\nasync function prepareConsoleErrorArgs(\n  entry: Extract<ServerLogEntry, { kind: 'any-logged-error' }>,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserialized = await Promise.all(\n    entry.args.map(async (arg) => {\n      if (arg.kind === 'arg') {\n        if (arg.isRejectionMessage) return red(arg.data)\n        return deserializeArgData(arg.data)\n      }\n      if (!arg.stack) return red(arg.prefix)\n      const mapped = await getSourceMappedStackFrames(arg.stack, ctx, distDir)\n      return colorError(mapped, { prefix: arg.prefix })\n    })\n  )\n\n  const mappedStack = await getSourceMappedStackFrames(\n    entry.consoleErrorStack,\n    ctx,\n    distDir\n  )\n\n  /**\n   * don't show the stack + codeblock when there are errors present, since:\n   * - it will look overwhelming to see 2 stacks and 2 code blocks\n   * - the user already knows where the console.error is at because we append the location\n   */\n  const location = getConsoleLocation(mappedStack)\n  if (entry.args.some((a) => a.kind === 'formatted-error-arg')) {\n    const result = stripFormatSpecifiers(deserialized)\n    if (location) {\n      result.push(dim(`(${location})`))\n    }\n    return result\n  }\n  const result = [\n    ...processConsoleFormatStrings(deserialized),\n    colorError(mappedStack),\n  ]\n  if (location) {\n    result.push(dim(`(${location})`))\n  }\n  return result\n}\n\nasync function handleTable(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserializedArgs = await Promise.all(\n    entry.args.map(async (arg: any) => {\n      if (arg.kind === 'formatted-error-arg') {\n        return { stack: arg.stack }\n      }\n      return deserializeArgData(arg.data)\n    })\n  )\n\n  const location = await (async () => {\n    if (!entry.consoleMethodStack) {\n      return\n    }\n    const frames = await getSourceMappedStackFrames(\n      entry.consoleMethodStack,\n      ctx,\n      distDir\n    )\n    return getConsoleLocation(frames)\n  })()\n\n  // we can't inline pass browser prefix, but it looks better multiline for table anyways\n  forwardConsole.log(browserPrefix)\n  forwardConsole.table(...deserializedArgs)\n  if (location) {\n    forwardConsole.log(dim(`(${location})`))\n  }\n}\n\nasync function handleTrace(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const deserializedArgs = await Promise.all(\n    entry.args.map(async (arg: any) => {\n      if (arg.kind === 'formatted-error-arg') {\n        if (!arg.stack) return red(arg.prefix)\n        const mapped = await getSourceMappedStackFrames(arg.stack, ctx, distDir)\n        return colorError(mapped, { prefix: arg.prefix })\n      }\n      return deserializeArgData(arg.data)\n    })\n  )\n\n  if (!entry.consoleMethodStack) {\n    forwardConsole.log(\n      browserPrefix,\n      ...deserializedArgs,\n      '[Trace unavailable]'\n    )\n    return\n  }\n\n  // TODO(rob): refactor so we can re-use result and not re-run the entire source map to avoid trivial post processing\n  const [mapped, mappedIgnored] = await Promise.all([\n    getSourceMappedStackFrames(entry.consoleMethodStack, ctx, distDir, false),\n    getSourceMappedStackFrames(entry.consoleMethodStack, ctx, distDir),\n  ])\n\n  const location = getConsoleLocation(mappedIgnored)\n  forwardConsole.log(\n    browserPrefix,\n    ...deserializedArgs,\n    `\\n${mapped.stack}`,\n    ...(location ? [`\\n${dim(`(${location})`)}`] : [])\n  )\n}\n\nasync function handleDir(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string\n) {\n  const loggableEntry = await prepareConsoleArgs(entry, ctx, distDir)\n  const consoleMethod =\n    (forwardConsole as any)[entry.method] || forwardConsole.log\n\n  if (entry.consoleMethodStack) {\n    const mapped = await getSourceMappedStackFrames(\n      entry.consoleMethodStack,\n      ctx,\n      distDir\n    )\n    const location = dim(`(${getConsoleLocation(mapped)})`)\n    const originalWrite = process.stdout.write.bind(process.stdout)\n    let captured = ''\n    process.stdout.write = (chunk) => {\n      captured += chunk\n      return true\n    }\n    try {\n      consoleMethod(...loggableEntry)\n    } finally {\n      process.stdout.write = originalWrite\n    }\n    const preserved = captured.replace(/\\r?\\n$/, '')\n    originalWrite(`${browserPrefix}${preserved} ${location}\\n`)\n    return\n  }\n  consoleMethod(browserPrefix, ...loggableEntry)\n}\n\nasync function handleDefaultConsole(\n  entry: ConsoleEntry<string>,\n  browserPrefix: string,\n  ctx: MappingContext,\n  distDir: string,\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n) {\n  const loggableEntry = await prepareConsoleArgs(entry, ctx, distDir)\n  const withStackEntry = await withLocation(\n    {\n      original: loggableEntry,\n      stack: (entry as any).consoleMethodStack || null,\n    },\n    ctx,\n    distDir,\n    config\n  )\n  const consoleMethod = forwardConsole[entry.method] || forwardConsole.log\n  ;(consoleMethod as (...args: any[]) => void)(browserPrefix, ...withStackEntry)\n}\n\nexport async function handleLog(\n  entries: ServerLogEntry[],\n  ctx: MappingContext,\n  distDir: string,\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n): Promise<void> {\n  const browserPrefix = cyan('[browser]')\n\n  for (const entry of entries) {\n    try {\n      switch (entry.kind) {\n        case 'console': {\n          switch (entry.method) {\n            case 'table': {\n              // timeout based abort on source mapping result\n              await handleTable(entry, browserPrefix, ctx, distDir)\n              break\n            }\n            // ignore frames\n            case 'trace': {\n              await handleTrace(entry, browserPrefix, ctx, distDir)\n              break\n            }\n            case 'dir': {\n              await handleDir(entry, browserPrefix, ctx, distDir)\n              break\n            }\n            // xml log thing maybe needs an impl\n\n            // [browser] undefined (app/page.tsx:8:11) console.group\n            // check console assert\n            default: {\n              await handleDefaultConsole(\n                entry,\n                browserPrefix,\n                ctx,\n                distDir,\n                config\n              )\n            }\n          }\n          break\n        }\n        // any logged errors are anything that are logged as \"red\" in the browser but aren't only an Error (console.error, Promise.reject(100))\n        case 'any-logged-error': {\n          const consoleArgs = await prepareConsoleErrorArgs(entry, ctx, distDir)\n          forwardConsole.error(browserPrefix, ...consoleArgs)\n          break\n        }\n        // formatted error is an explicit error event (rejections, uncaught errors)\n        case 'formatted-error': {\n          const formattedArgs = await prepareFormattedErrorArgs(\n            entry,\n            ctx,\n            distDir\n          )\n          forwardConsole.error(browserPrefix, ...formattedArgs)\n          break\n        }\n        default: {\n        }\n      }\n    } catch {\n      switch (entry.kind) {\n        case 'any-logged-error': {\n          const consoleArgs = await prepareConsoleErrorArgs(entry, ctx, distDir)\n          forwardConsole.error(browserPrefix, ...consoleArgs)\n          break\n        }\n        case 'console': {\n          const consoleMethod =\n            forwardConsole[entry.method] || forwardConsole.log\n          const consoleArgs = await prepareConsoleArgs(entry, ctx, distDir)\n          ;(consoleMethod as (...args: any[]) => void)(\n            browserPrefix,\n            ...consoleArgs\n          )\n          break\n        }\n        case 'formatted-error': {\n          forwardConsole.error(browserPrefix, `${entry.prefix}\\n`, entry.stack)\n          break\n        }\n        default: {\n        }\n      }\n    }\n  }\n}\n\n// the data is used later when we need to get sourcemaps for error stacks\nexport async function receiveBrowserLogsWebpack(opts: {\n  entries: ServerLogEntry[]\n  router: 'app' | 'pages'\n  sourceType?: 'server' | 'edge-server'\n  clientStats: () => any\n  serverStats: () => any\n  edgeServerStats: () => any\n  rootDirectory: string\n  distDir: string\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n}): Promise<void> {\n  const {\n    entries,\n    router,\n    sourceType,\n    clientStats,\n    serverStats,\n    edgeServerStats,\n    rootDirectory,\n    distDir,\n  } = opts\n\n  const isAppDirectory = router === 'app'\n  const isServer = sourceType === 'server'\n  const isEdgeServer = sourceType === 'edge-server'\n\n  const ctx: MappingContext = {\n    bundler: 'webpack',\n    isServer,\n    isEdgeServer,\n    isAppDirectory,\n    clientStats,\n    serverStats,\n    edgeServerStats,\n    rootDirectory,\n  }\n\n  await handleLog(entries, ctx, distDir, opts.config)\n}\n\nexport async function receiveBrowserLogsTurbopack(opts: {\n  entries: ServerLogEntry[]\n  router: 'app' | 'pages'\n  sourceType?: 'server' | 'edge-server'\n  project: Project\n  projectPath: string\n  distDir: string\n  config: boolean | { logDepth?: number; showSourceLocation?: boolean }\n}): Promise<void> {\n  const { entries, router, sourceType, project, projectPath, distDir } = opts\n\n  const isAppDirectory = router === 'app'\n  const isServer = sourceType === 'server'\n  const isEdgeServer = sourceType === 'edge-server'\n\n  const ctx: MappingContext = {\n    bundler: 'turbopack',\n    project,\n    projectPath,\n    isServer,\n    isEdgeServer,\n    isAppDirectory,\n  }\n\n  await handleLog(entries, ctx, distDir, opts.config)\n}\n"], "names": ["handleLog", "receiveBrowserLogsTurbopack", "receiveBrowserLogsWebpack", "restoreUndefined", "stripFormatSpecifiers", "x", "UNDEFINED_MARKER", "undefined", "Array", "isArray", "map", "k", "methods", "methodsToSkipInspect", "Set", "forwardConsole", "console", "Object", "fromEntries", "method", "args", "arg", "has", "util", "inspect", "depth", "Infinity", "colors", "deserializeArgData", "JSON", "parse", "colorError", "mapped", "config", "colorFn", "applyColor", "red", "kind", "prefix", "stack", "frameCode", "processConsoleFormatStrings", "length", "formatString", "includes", "formatted", "format", "fmtIn", "String", "rest", "slice", "fmtOut", "argPtr", "i", "token", "obj", "result", "push", "prepareFormattedErrorArgs", "entry", "ctx", "distDir", "getSourceMappedStackFrames", "prepareConsoleArgs", "deserialized", "Promise", "all", "data", "yellow", "prepareConsoleErrorArgs", "isRejectionMessage", "mappedStack", "consoleErrorStack", "location", "getConsoleLocation", "some", "a", "dim", "handleTable", "browserPrefix", "deserializedArgs", "consoleMethodStack", "frames", "log", "table", "handleTrace", "mappedIgnored", "handleDir", "loggableEntry", "consoleMethod", "originalWrite", "process", "stdout", "write", "bind", "captured", "chunk", "preserved", "replace", "handleDefaultConsole", "withStackEntry", "withLocation", "original", "entries", "cyan", "consoleArgs", "error", "formattedArgs", "opts", "router", "sourceType", "clientStats", "serverStats", "edgeServerStats", "rootDirectory", "isAppDirectory", "isServer", "isEdgeServer", "bundler", "project", "projectPath"], "mappings": ";;;;;;;;;;;;;;;;;;IA+Zs<PERSON>,SAAS;eAATA;;IAmIAC,2BAA2B;eAA3BA;;IAxCAC,yBAAyB;eAAzBA;;IA1eNC,gBAAgB;eAAhBA;;IAqIAC,qBAAqB;eAArBA;;;4BArJuB;6DAEtB;2BAMV;mCAMA;;;;;;AAEA,SAASD,iBAAiBE,CAAM;IACrC,IAAIA,MAAMC,mCAAgB,EAAE,OAAOC;IACnC,IAAIC,MAAMC,OAAO,CAACJ,IAAI,OAAOA,EAAEK,GAAG,CAACP;IACnC,IAAIE,KAAK,OAAOA,MAAM,UAAU;QAC9B,IAAK,IAAIM,KAAKN,EAAG;YACfA,CAAC,CAACM,EAAE,GAAGR,iBAAiBE,CAAC,CAACM,EAAE;QAC9B;IACF;IACA,OAAON;AACT;AAEA,MAAMO,UAA4B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,uBAAuB,IAAIC,IAAI;IACnC;IACA;IACA;IACA;IACA;IACA;CACD;AAED,oHAAoH;AACpH,MAAMC,iBAAiC;IACrC,GAAGC,OAAO;IACV,GAAGC,OAAOC,WAAW,CACnBN,QAAQF,GAAG,CAAC,CAACS,SAAW;YACtBA;YACA,CAAC,GAAGC,OACF,AAACJ,OAAO,CAACG,OAAO,IACXC,KAAKV,GAAG,CAAC,CAACW,MACXR,qBAAqBS,GAAG,CAACH,WACzB,OAAOE,QAAQ,YACfA,QAAQ,OACJA,MAEAE,aAAI,CAACC,OAAO,CAACH,KAAK;wBAAEI,OAAOC;wBAAUC,QAAQ;oBAAK;SAG7D,EACF;AACH;AAEA,eAAeC,mBAAmBP,GAAQ;IACxC,IAAI;QACF,oJAAoJ;QACpJ,IAAIA,QAAQf,mCAAgB,EAAE;YAC5B,OAAOH,iBAAiBkB;QAC1B;QAEA,OAAOlB,iBAAiB0B,KAAKC,KAAK,CAACT;IACrC,EAAE,OAAM;QACN,OAAOA;IACT;AACF;AAEA,MAAMU,aAAa,CACjBC,QACAC;IAKA,MAAMC,UACJD,CAAAA,0BAAAA,OAAQE,UAAU,MAAK5B,aAAa0B,OAAOE,UAAU,GAAGC,eAAG,GAAG,CAAI/B,IAASA;IAC7E,OAAQ2B,OAAOK,IAAI;QACjB,KAAK;QACL,KAAK;YAAS;gBACZ,OACE,AAACJ,CAAAA,CAAAA,0BAAAA,OAAQK,MAAM,IAAGJ,QAAQD,0BAAAA,OAAQK,MAAM,IAAI,EAAC,IAC7C,CAAC,EAAE,EAAEJ,QAAQF,OAAOO,KAAK,GAAG;YAEhC;QACA,KAAK;YAAmB;gBACtB,OACE,AAACN,CAAAA,CAAAA,0BAAAA,OAAQK,MAAM,IAAGJ,QAAQD,0BAAAA,OAAQK,MAAM,IAAI,EAAC,IAC7C,CAAC,EAAE,EAAEJ,QAAQF,OAAOO,KAAK,EAAE,EAAE,EAAEP,OAAOQ,SAAS,EAAE;YAErD;QACA,yIAAyI;QACzI,KAAK;YAAe;gBAClB,OAAOP,CAAAA,0BAAAA,OAAQK,MAAM,IAAGJ,QAAQD,0BAAAA,OAAQK,MAAM,IAAI;YACpD;QACA;YAAS,CACT;IACF;IACAN;AACF;AAEA,SAASS,4BAA4BrB,IAAW;IAC9C;;;GAGC,GACD,IAAIA,KAAKsB,MAAM,GAAG,KAAK,OAAOtB,IAAI,CAAC,EAAE,KAAK,UAAU;QAClD,MAAMuB,eAAevB,IAAI,CAAC,EAAE;QAC5B,IACEuB,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,SACtBD,aAAaC,QAAQ,CAAC,OACtB;YACA,IAAI;gBACF,MAAMC,YAAYtB,aAAI,CAACuB,MAAM,IAAI1B;gBACjC,OAAO;oBAACyB;iBAAU;YACpB,EAAE,OAAM;gBACN,OAAOzB;YACT;QACF;IACF;IACA,OAAOA;AACT;AAMO,SAAShB,sBAAsBgB,IAAW;IAC/C,IAAIA,KAAKsB,MAAM,KAAK,KAAK,OAAOtB,IAAI,CAAC,EAAE,KAAK,UAAU,OAAOA;IAE7D,MAAM2B,QAAQC,OAAO5B,IAAI,CAAC,EAAE;IAC5B,MAAM6B,OAAO7B,KAAK8B,KAAK,CAAC;IAExB,IAAI,CAACH,MAAMH,QAAQ,CAAC,MAAM,OAAOxB;IAEjC,IAAI+B,SAAS;IACb,IAAIC,SAAS;IAEb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,MAAML,MAAM,EAAEW,IAAK;QACrC,IAAIN,KAAK,CAACM,EAAE,KAAK,KAAK;YACpBF,UAAUJ,KAAK,CAACM,EAAE;YAClB;QACF;QAEA,IAAIN,KAAK,CAACM,IAAI,EAAE,KAAK,KAAK;YACxBF,UAAU;YACVE;YACA;QACF;QAEA,MAAMC,QAAQP,KAAK,CAAC,EAAEM,EAAE;QAExB,IAAI,CAACC,OAAO;YACVH,UAAU;YACV;QACF;QAEA,IAAI,WAAWP,QAAQ,CAACU,UAAUA,UAAU,KAAK;YAC/C,IAAIF,SAASH,KAAKP,MAAM,EAAE;gBACxB,IAAIY,UAAU,KAAK;oBACjBF;gBACF,OAAO,IAAIE,UAAU,OAAOA,UAAU,OAAOA,UAAU,KAAK;oBAC1D,MAAMC,MAAMN,IAAI,CAACG,SAAS;oBAC1BD,UAAU5B,aAAI,CAACC,OAAO,CAAC+B,KAAK;wBAAE9B,OAAO;wBAAGE,QAAQ;oBAAM;gBACxD,OAAO;oBACL,+CAA+C;oBAC/CwB,UAAUH,OAAOC,IAAI,CAACG,SAAS;gBACjC;YACF;YACA;QACF;QAEAD,UAAU,MAAMG;IAClB;IAEA,MAAME,SAAS;QAACL;KAAO;IACvB,IAAIC,SAASH,KAAKP,MAAM,EAAE;QACxBc,OAAOC,IAAI,IAAIR,KAAKC,KAAK,CAACE;IAC5B;IAEA,OAAOI;AACT;AAEA,eAAeE,0BACbC,KAA2D,EAC3DC,GAAmB,EACnBC,OAAe;IAEf,MAAM7B,SAAS,MAAM8B,IAAAA,qCAA0B,EAACH,MAAMpB,KAAK,EAAEqB,KAAKC;IAClE,OAAO;QAAC9B,WAAWC,QAAQ;YAAEM,QAAQqB,MAAMrB,MAAM;QAAC;KAAG;AACvD;AAEA,eAAeyB,mBACbJ,KAAmD,EACnDC,GAAmB,EACnBC,OAAe;IAEf,MAAMG,eAAe,MAAMC,QAAQC,GAAG,CACpCP,MAAMvC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIgB,IAAI,KAAK,OAAO;YACtB,MAAM8B,OAAO,MAAMvC,mBAAmBP,IAAI8C,IAAI;YAC9C,IAAIR,MAAMxC,MAAM,KAAK,UAAU,OAAOgD,SAAS,UAAU;gBACvD,OAAOC,IAAAA,kBAAM,EAACD;YAChB;YACA,OAAOA;QACT;QACA,IAAI,CAAC9C,IAAIkB,KAAK,EAAE,OAAOH,IAAAA,eAAG,EAACf,IAAIiB,MAAM;QACrC,MAAMN,SAAS,MAAM8B,IAAAA,qCAA0B,EAACzC,IAAIkB,KAAK,EAAEqB,KAAKC;QAChE,OAAO9B,WAAWC,QAAQ;YAAEM,QAAQjB,IAAIiB,MAAM;YAAEH,YAAY;QAAM;IACpE;IAGF,OAAOM,4BAA4BuB;AACrC;AAEA,eAAeK,wBACbV,KAA4D,EAC5DC,GAAmB,EACnBC,OAAe;IAEf,MAAMG,eAAe,MAAMC,QAAQC,GAAG,CACpCP,MAAMvC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIgB,IAAI,KAAK,OAAO;YACtB,IAAIhB,IAAIiD,kBAAkB,EAAE,OAAOlC,IAAAA,eAAG,EAACf,IAAI8C,IAAI;YAC/C,OAAOvC,mBAAmBP,IAAI8C,IAAI;QACpC;QACA,IAAI,CAAC9C,IAAIkB,KAAK,EAAE,OAAOH,IAAAA,eAAG,EAACf,IAAIiB,MAAM;QACrC,MAAMN,SAAS,MAAM8B,IAAAA,qCAA0B,EAACzC,IAAIkB,KAAK,EAAEqB,KAAKC;QAChE,OAAO9B,WAAWC,QAAQ;YAAEM,QAAQjB,IAAIiB,MAAM;QAAC;IACjD;IAGF,MAAMiC,cAAc,MAAMT,IAAAA,qCAA0B,EAClDH,MAAMa,iBAAiB,EACvBZ,KACAC;IAGF;;;;GAIC,GACD,MAAMY,WAAWC,IAAAA,6BAAkB,EAACH;IACpC,IAAIZ,MAAMvC,IAAI,CAACuD,IAAI,CAAC,CAACC,IAAMA,EAAEvC,IAAI,KAAK,wBAAwB;QAC5D,MAAMmB,SAASpD,sBAAsB4D;QACrC,IAAIS,UAAU;YACZjB,OAAOC,IAAI,CAACoB,IAAAA,eAAG,EAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC;QACjC;QACA,OAAOjB;IACT;IACA,MAAMA,SAAS;WACVf,4BAA4BuB;QAC/BjC,WAAWwC;KACZ;IACD,IAAIE,UAAU;QACZjB,OAAOC,IAAI,CAACoB,IAAAA,eAAG,EAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC;IACjC;IACA,OAAOjB;AACT;AAEA,eAAesB,YACbnB,KAA2B,EAC3BoB,aAAqB,EACrBnB,GAAmB,EACnBC,OAAe;IAEf,MAAMmB,mBAAmB,MAAMf,QAAQC,GAAG,CACxCP,MAAMvC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIgB,IAAI,KAAK,uBAAuB;YACtC,OAAO;gBAAEE,OAAOlB,IAAIkB,KAAK;YAAC;QAC5B;QACA,OAAOX,mBAAmBP,IAAI8C,IAAI;IACpC;IAGF,MAAMM,WAAW,MAAM,AAAC,CAAA;QACtB,IAAI,CAACd,MAAMsB,kBAAkB,EAAE;YAC7B;QACF;QACA,MAAMC,SAAS,MAAMpB,IAAAA,qCAA0B,EAC7CH,MAAMsB,kBAAkB,EACxBrB,KACAC;QAEF,OAAOa,IAAAA,6BAAkB,EAACQ;IAC5B,CAAA;IAEA,uFAAuF;IACvFnE,eAAeoE,GAAG,CAACJ;IACnBhE,eAAeqE,KAAK,IAAIJ;IACxB,IAAIP,UAAU;QACZ1D,eAAeoE,GAAG,CAACN,IAAAA,eAAG,EAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC;IACxC;AACF;AAEA,eAAeY,YACb1B,KAA2B,EAC3BoB,aAAqB,EACrBnB,GAAmB,EACnBC,OAAe;IAEf,MAAMmB,mBAAmB,MAAMf,QAAQC,GAAG,CACxCP,MAAMvC,IAAI,CAACV,GAAG,CAAC,OAAOW;QACpB,IAAIA,IAAIgB,IAAI,KAAK,uBAAuB;YACtC,IAAI,CAAChB,IAAIkB,KAAK,EAAE,OAAOH,IAAAA,eAAG,EAACf,IAAIiB,MAAM;YACrC,MAAMN,SAAS,MAAM8B,IAAAA,qCAA0B,EAACzC,IAAIkB,KAAK,EAAEqB,KAAKC;YAChE,OAAO9B,WAAWC,QAAQ;gBAAEM,QAAQjB,IAAIiB,MAAM;YAAC;QACjD;QACA,OAAOV,mBAAmBP,IAAI8C,IAAI;IACpC;IAGF,IAAI,CAACR,MAAMsB,kBAAkB,EAAE;QAC7BlE,eAAeoE,GAAG,CAChBJ,kBACGC,kBACH;QAEF;IACF;IAEA,oHAAoH;IACpH,MAAM,CAAChD,QAAQsD,cAAc,GAAG,MAAMrB,QAAQC,GAAG,CAAC;QAChDJ,IAAAA,qCAA0B,EAACH,MAAMsB,kBAAkB,EAAErB,KAAKC,SAAS;QACnEC,IAAAA,qCAA0B,EAACH,MAAMsB,kBAAkB,EAAErB,KAAKC;KAC3D;IAED,MAAMY,WAAWC,IAAAA,6BAAkB,EAACY;IACpCvE,eAAeoE,GAAG,CAChBJ,kBACGC,kBACH,CAAC,EAAE,EAAEhD,OAAOO,KAAK,EAAE,KACfkC,WAAW;QAAC,CAAC,EAAE,EAAEI,IAAAA,eAAG,EAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC,GAAG;KAAC,GAAG,EAAE;AAErD;AAEA,eAAec,UACb5B,KAA2B,EAC3BoB,aAAqB,EACrBnB,GAAmB,EACnBC,OAAe;IAEf,MAAM2B,gBAAgB,MAAMzB,mBAAmBJ,OAAOC,KAAKC;IAC3D,MAAM4B,gBACJ,AAAC1E,cAAsB,CAAC4C,MAAMxC,MAAM,CAAC,IAAIJ,eAAeoE,GAAG;IAE7D,IAAIxB,MAAMsB,kBAAkB,EAAE;QAC5B,MAAMjD,SAAS,MAAM8B,IAAAA,qCAA0B,EAC7CH,MAAMsB,kBAAkB,EACxBrB,KACAC;QAEF,MAAMY,WAAWI,IAAAA,eAAG,EAAC,CAAC,CAAC,EAAEH,IAAAA,6BAAkB,EAAC1C,QAAQ,CAAC,CAAC;QACtD,MAAM0D,gBAAgBC,QAAQC,MAAM,CAACC,KAAK,CAACC,IAAI,CAACH,QAAQC,MAAM;QAC9D,IAAIG,WAAW;QACfJ,QAAQC,MAAM,CAACC,KAAK,GAAG,CAACG;YACtBD,YAAYC;YACZ,OAAO;QACT;QACA,IAAI;YACFP,iBAAiBD;QACnB,SAAU;YACRG,QAAQC,MAAM,CAACC,KAAK,GAAGH;QACzB;QACA,MAAMO,YAAYF,SAASG,OAAO,CAAC,UAAU;QAC7CR,cAAc,GAAGX,gBAAgBkB,UAAU,CAAC,EAAExB,SAAS,EAAE,CAAC;QAC1D;IACF;IACAgB,cAAcV,kBAAkBS;AAClC;AAEA,eAAeW,qBACbxC,KAA2B,EAC3BoB,aAAqB,EACrBnB,GAAmB,EACnBC,OAAe,EACf5B,MAAqE;IAErE,MAAMuD,gBAAgB,MAAMzB,mBAAmBJ,OAAOC,KAAKC;IAC3D,MAAMuC,iBAAiB,MAAMC,IAAAA,uBAAY,EACvC;QACEC,UAAUd;QACVjD,OAAO,AAACoB,MAAcsB,kBAAkB,IAAI;IAC9C,GACArB,KACAC,SACA5B;IAEF,MAAMwD,gBAAgB1E,cAAc,CAAC4C,MAAMxC,MAAM,CAAC,IAAIJ,eAAeoE,GAAG;IACtEM,cAA2CV,kBAAkBqB;AACjE;AAEO,eAAepG,UACpBuG,OAAyB,EACzB3C,GAAmB,EACnBC,OAAe,EACf5B,MAAqE;IAErE,MAAM8C,gBAAgByB,IAAAA,gBAAI,EAAC;IAE3B,KAAK,MAAM7C,SAAS4C,QAAS;QAC3B,IAAI;YACF,OAAQ5C,MAAMtB,IAAI;gBAChB,KAAK;oBAAW;wBACd,OAAQsB,MAAMxC,MAAM;4BAClB,KAAK;gCAAS;oCACZ,+CAA+C;oCAC/C,MAAM2D,YAAYnB,OAAOoB,eAAenB,KAAKC;oCAC7C;gCACF;4BACA,gBAAgB;4BAChB,KAAK;gCAAS;oCACZ,MAAMwB,YAAY1B,OAAOoB,eAAenB,KAAKC;oCAC7C;gCACF;4BACA,KAAK;gCAAO;oCACV,MAAM0B,UAAU5B,OAAOoB,eAAenB,KAAKC;oCAC3C;gCACF;4BACA,oCAAoC;4BAEpC,wDAAwD;4BACxD,uBAAuB;4BACvB;gCAAS;oCACP,MAAMsC,qBACJxC,OACAoB,eACAnB,KACAC,SACA5B;gCAEJ;wBACF;wBACA;oBACF;gBACA,uIAAuI;gBACvI,KAAK;oBAAoB;wBACvB,MAAMwE,cAAc,MAAMpC,wBAAwBV,OAAOC,KAAKC;wBAC9D9C,eAAe2F,KAAK,CAAC3B,kBAAkB0B;wBACvC;oBACF;gBACA,2EAA2E;gBAC3E,KAAK;oBAAmB;wBACtB,MAAME,gBAAgB,MAAMjD,0BAC1BC,OACAC,KACAC;wBAEF9C,eAAe2F,KAAK,CAAC3B,kBAAkB4B;wBACvC;oBACF;gBACA;oBAAS,CACT;YACF;QACF,EAAE,OAAM;YACN,OAAQhD,MAAMtB,IAAI;gBAChB,KAAK;oBAAoB;wBACvB,MAAMoE,cAAc,MAAMpC,wBAAwBV,OAAOC,KAAKC;wBAC9D9C,eAAe2F,KAAK,CAAC3B,kBAAkB0B;wBACvC;oBACF;gBACA,KAAK;oBAAW;wBACd,MAAMhB,gBACJ1E,cAAc,CAAC4C,MAAMxC,MAAM,CAAC,IAAIJ,eAAeoE,GAAG;wBACpD,MAAMsB,cAAc,MAAM1C,mBAAmBJ,OAAOC,KAAKC;wBACvD4B,cACAV,kBACG0B;wBAEL;oBACF;gBACA,KAAK;oBAAmB;wBACtB1F,eAAe2F,KAAK,CAAC3B,eAAe,GAAGpB,MAAMrB,MAAM,CAAC,EAAE,CAAC,EAAEqB,MAAMpB,KAAK;wBACpE;oBACF;gBACA;oBAAS,CACT;YACF;QACF;IACF;AACF;AAGO,eAAerC,0BAA0B0G,IAU/C;IACC,MAAM,EACJL,OAAO,EACPM,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,eAAe,EACfC,aAAa,EACbrD,OAAO,EACR,GAAG+C;IAEJ,MAAMO,iBAAiBN,WAAW;IAClC,MAAMO,WAAWN,eAAe;IAChC,MAAMO,eAAeP,eAAe;IAEpC,MAAMlD,MAAsB;QAC1B0D,SAAS;QACTF;QACAC;QACAF;QACAJ;QACAC;QACAC;QACAC;IACF;IAEA,MAAMlH,UAAUuG,SAAS3C,KAAKC,SAAS+C,KAAK3E,MAAM;AACpD;AAEO,eAAehC,4BAA4B2G,IAQjD;IACC,MAAM,EAAEL,OAAO,EAAEM,MAAM,EAAEC,UAAU,EAAES,OAAO,EAAEC,WAAW,EAAE3D,OAAO,EAAE,GAAG+C;IAEvE,MAAMO,iBAAiBN,WAAW;IAClC,MAAMO,WAAWN,eAAe;IAChC,MAAMO,eAAeP,eAAe;IAEpC,MAAMlD,MAAsB;QAC1B0D,SAAS;QACTC;QACAC;QACAJ;QACAC;QACAF;IACF;IAEA,MAAMnH,UAAUuG,SAAS3C,KAAKC,SAAS+C,KAAK3E,MAAM;AACpD", "ignoreList": [0]}
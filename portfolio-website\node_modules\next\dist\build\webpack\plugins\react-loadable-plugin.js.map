{"version": 3, "sources": ["../../../../src/build/webpack/plugins/react-loadable-plugin.ts"], "sourcesContent": ["/**\nCOPYRIGHT (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR\n*/\n// Implementation of this PR: https://github.com/jamiebuilds/react-loadable/pull/132\n// Modified to strip out unneeded results for Next's specific use case\n\nimport type {\n  DynamicCssManifest,\n  ReactLoadableManifest,\n} from '../../../server/load-components'\nimport path from 'path'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport { DYNAMIC_CSS_MANIFEST } from '../../../shared/lib/constants'\n\nfunction getModuleId(compilation: any, module: any): string | number {\n  return compilation.chunkGraph.getModuleId(module)\n}\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string } {\n  return compilation.moduleGraph.getModule(dep)\n}\n\nfunction getOriginModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string } {\n  return compilation.moduleGraph.getParentModule(dep)\n}\n\nfunction getChunkGroupFromBlock(\n  compilation: any,\n  block: any\n): webpack.Compilation['chunkGroups'] {\n  return compilation.chunkGraph.getBlockChunkGroup(block)\n}\n\nfunction buildManifest(\n  _compiler: webpack.Compiler,\n  compilation: webpack.Compilation,\n  projectSrcDir: string | undefined,\n  dev: boolean,\n  shouldCreateDynamicCssManifest: boolean\n): {\n  reactLoadableManifest: ReactLoadableManifest\n  dynamicCssManifest: DynamicCssManifest\n} {\n  if (!projectSrcDir) {\n    return {\n      reactLoadableManifest: {},\n      dynamicCssManifest: [],\n    }\n  }\n  const dynamicCssManifestSet = new Set<string>()\n  let manifest: ReactLoadableManifest = {}\n\n  // This is allowed:\n  // import(\"./module\"); <- ImportDependency\n\n  // We don't support that:\n  // import(/* webpackMode: \"eager\" */ \"./module\") <- ImportEagerDependency\n  // import(`./module/${param}`) <- ImportContextDependency\n\n  // Find all dependencies blocks which contains a `import()` dependency\n  const handleBlock = (block: any) => {\n    block.blocks.forEach(handleBlock)\n    const chunkGroup = getChunkGroupFromBlock(compilation, block)\n    for (const dependency of block.dependencies) {\n      if (dependency.type.startsWith('import()')) {\n        // get the referenced module\n        const module = getModuleFromDependency(compilation, dependency)\n        if (!module) return\n\n        // get the module containing the import()\n        const originModule = getOriginModuleFromDependency(\n          compilation,\n          dependency\n        )\n        const originRequest: string | undefined = originModule?.resource\n        if (!originRequest) return\n\n        // We construct a \"unique\" key from origin module and request\n        // It's not perfect unique, but that will be fine for us.\n        // We also need to construct the same in the babel plugin.\n        const key = `${path.relative(projectSrcDir, originRequest)} -> ${\n          dependency.request\n        }`\n\n        // Capture all files that need to be loaded.\n        const files = new Set<string>()\n\n        if (manifest[key]) {\n          // In the \"rare\" case where multiple chunk groups\n          // are created for the same `import()` or multiple\n          // import()s reference the same module, we merge\n          // the files to make sure to not miss files\n          // This may cause overfetching in edge cases.\n          for (const file of manifest[key].files) {\n            files.add(file)\n          }\n        }\n\n        // There might not be a chunk group when all modules\n        // are already loaded. In this case we only need need\n        // the module id and no files\n        if (chunkGroup) {\n          for (const chunk of (chunkGroup as any)\n            .chunks as webpack.Compilation['chunks']) {\n            chunk.files.forEach((file: string) => {\n              if (\n                (file.endsWith('.js') || file.endsWith('.css')) &&\n                file.match(/^static\\/(chunks|css)\\//)\n              ) {\n                files.add(file)\n\n                if (shouldCreateDynamicCssManifest && file.endsWith('.css')) {\n                  dynamicCssManifestSet.add(file)\n                }\n              }\n            })\n          }\n        }\n\n        // usually we have to add the parent chunk groups too\n        // but we assume that all parents are also imported by\n        // next/dynamic so they are loaded by the same technique\n\n        // add the id and files to the manifest\n        const id = dev ? key : getModuleId(compilation, module)\n        manifest[key] = { id, files: Array.from(files) }\n      }\n    }\n  }\n  for (const module of compilation.modules) {\n    module.blocks.forEach(handleBlock)\n  }\n\n  manifest = Object.keys(manifest)\n    .sort()\n    // eslint-disable-next-line no-sequences\n    .reduce((a, c) => ((a[c] = manifest[c]), a), {} as any)\n\n  return {\n    reactLoadableManifest: manifest,\n    dynamicCssManifest: Array.from(dynamicCssManifestSet),\n  }\n}\n\nexport class ReactLoadablePlugin {\n  private filename: string\n  private pagesOrAppDir: string | undefined\n  private isPagesDir: boolean\n  private runtimeAsset?: string\n  private dev: boolean\n\n  constructor(opts: {\n    filename: string\n    pagesDir?: string\n    appDir?: string\n    runtimeAsset?: string\n    dev: boolean\n  }) {\n    this.filename = opts.filename\n    this.pagesOrAppDir = opts.pagesDir || opts.appDir\n    this.isPagesDir = Boolean(opts.pagesDir)\n    this.runtimeAsset = opts.runtimeAsset\n    this.dev = opts.dev\n  }\n\n  createAssets(compiler: any, compilation: any) {\n    const projectSrcDir = this.pagesOrAppDir\n      ? path.dirname(this.pagesOrAppDir)\n      : undefined\n    const shouldCreateDynamicCssManifest = !this.dev && this.isPagesDir\n    const { reactLoadableManifest, dynamicCssManifest } = buildManifest(\n      compiler,\n      compilation,\n      projectSrcDir,\n      this.dev,\n      shouldCreateDynamicCssManifest\n    )\n\n    compilation.emitAsset(\n      this.filename,\n      new sources.RawSource(JSON.stringify(reactLoadableManifest, null, 2))\n    )\n\n    if (this.runtimeAsset) {\n      compilation.emitAsset(\n        this.runtimeAsset,\n        new sources.RawSource(\n          `self.__REACT_LOADABLE_MANIFEST=${JSON.stringify(\n            JSON.stringify(reactLoadableManifest)\n          )}`\n        )\n      )\n    }\n\n    // This manifest prevents removing server rendered <link> tags after client\n    // navigation. This is only needed under Pages dir && Production && Webpack.\n    // x-ref: https://github.com/vercel/next.js/pull/72959\n    if (shouldCreateDynamicCssManifest) {\n      compilation.emitAsset(\n        `${DYNAMIC_CSS_MANIFEST}.json`,\n        new sources.RawSource(JSON.stringify(dynamicCssManifest, null, 2))\n      )\n\n      // This is for edge runtime.\n      compilation.emitAsset(\n        `server/${DYNAMIC_CSS_MANIFEST}.js`,\n        new sources.RawSource(\n          `self.__DYNAMIC_CSS_MANIFEST=${JSON.stringify(\n            JSON.stringify(dynamicCssManifest)\n          )}`\n        )\n      )\n    }\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.make.tap('ReactLoadableManifest', (compilation) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: 'ReactLoadableManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => {\n          this.createAssets(compiler, compilation)\n        }\n      )\n    })\n  }\n}\n"], "names": ["ReactLoadablePlugin", "getModuleId", "compilation", "module", "chunkGraph", "getModuleFromDependency", "dep", "moduleGraph", "getModule", "getOriginModuleFromDependency", "getParentModule", "getChunkGroupFromBlock", "block", "getBlockChunkGroup", "buildManifest", "_compiler", "projectSrcDir", "dev", "shouldCreateDynamicCssManifest", "reactLoadableManifest", "dynamicCssManifest", "dynamicCssManifestSet", "Set", "manifest", "handleBlock", "blocks", "for<PERSON>ach", "chunkGroup", "dependency", "dependencies", "type", "startsWith", "originModule", "originRequest", "resource", "key", "path", "relative", "request", "files", "file", "add", "chunk", "chunks", "endsWith", "match", "id", "Array", "from", "modules", "Object", "keys", "sort", "reduce", "a", "c", "constructor", "opts", "filename", "pagesOrAppDir", "pagesDir", "appDir", "isPagesDir", "Boolean", "runtimeAsset", "createAssets", "compiler", "dirname", "undefined", "emitAsset", "sources", "RawSource", "JSON", "stringify", "DYNAMIC_CSS_MANIFEST", "apply", "hooks", "make", "tap", "processAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,GACA,oFAAoF;AACpF,sEAAsE;;;;;+BAkJzDA;;;eAAAA;;;6DA5II;yBACgB;2BACI;;;;;;AAErC,SAASC,YAAYC,WAAgB,EAAEC,MAAW;IAChD,OAAOD,YAAYE,UAAU,CAACH,WAAW,CAACE;AAC5C;AAEA,SAASE,wBACPH,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEA,SAASG,8BACPP,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACG,eAAe,CAACJ;AACjD;AAEA,SAASK,uBACPT,WAAgB,EAChBU,KAAU;IAEV,OAAOV,YAAYE,UAAU,CAACS,kBAAkB,CAACD;AACnD;AAEA,SAASE,cACPC,SAA2B,EAC3Bb,WAAgC,EAChCc,aAAiC,EACjCC,GAAY,EACZC,8BAAuC;IAKvC,IAAI,CAACF,eAAe;QAClB,OAAO;YACLG,uBAAuB,CAAC;YACxBC,oBAAoB,EAAE;QACxB;IACF;IACA,MAAMC,wBAAwB,IAAIC;IAClC,IAAIC,WAAkC,CAAC;IAEvC,mBAAmB;IACnB,0CAA0C;IAE1C,yBAAyB;IACzB,yEAAyE;IACzE,yDAAyD;IAEzD,sEAAsE;IACtE,MAAMC,cAAc,CAACZ;QACnBA,MAAMa,MAAM,CAACC,OAAO,CAACF;QACrB,MAAMG,aAAahB,uBAAuBT,aAAaU;QACvD,KAAK,MAAMgB,cAAchB,MAAMiB,YAAY,CAAE;YAC3C,IAAID,WAAWE,IAAI,CAACC,UAAU,CAAC,aAAa;gBAC1C,4BAA4B;gBAC5B,MAAM5B,SAASE,wBAAwBH,aAAa0B;gBACpD,IAAI,CAACzB,QAAQ;gBAEb,yCAAyC;gBACzC,MAAM6B,eAAevB,8BACnBP,aACA0B;gBAEF,MAAMK,gBAAoCD,gCAAAA,aAAcE,QAAQ;gBAChE,IAAI,CAACD,eAAe;gBAEpB,6DAA6D;gBAC7D,yDAAyD;gBACzD,0DAA0D;gBAC1D,MAAME,MAAM,GAAGC,aAAI,CAACC,QAAQ,CAACrB,eAAeiB,eAAe,IAAI,EAC7DL,WAAWU,OAAO,EAClB;gBAEF,4CAA4C;gBAC5C,MAAMC,QAAQ,IAAIjB;gBAElB,IAAIC,QAAQ,CAACY,IAAI,EAAE;oBACjB,iDAAiD;oBACjD,kDAAkD;oBAClD,gDAAgD;oBAChD,2CAA2C;oBAC3C,6CAA6C;oBAC7C,KAAK,MAAMK,QAAQjB,QAAQ,CAACY,IAAI,CAACI,KAAK,CAAE;wBACtCA,MAAME,GAAG,CAACD;oBACZ;gBACF;gBAEA,oDAAoD;gBACpD,qDAAqD;gBACrD,6BAA6B;gBAC7B,IAAIb,YAAY;oBACd,KAAK,MAAMe,SAAS,AAACf,WAClBgB,MAAM,CAAmC;wBAC1CD,MAAMH,KAAK,CAACb,OAAO,CAAC,CAACc;4BACnB,IACE,AAACA,CAAAA,KAAKI,QAAQ,CAAC,UAAUJ,KAAKI,QAAQ,CAAC,OAAM,KAC7CJ,KAAKK,KAAK,CAAC,4BACX;gCACAN,MAAME,GAAG,CAACD;gCAEV,IAAItB,kCAAkCsB,KAAKI,QAAQ,CAAC,SAAS;oCAC3DvB,sBAAsBoB,GAAG,CAACD;gCAC5B;4BACF;wBACF;oBACF;gBACF;gBAEA,qDAAqD;gBACrD,sDAAsD;gBACtD,wDAAwD;gBAExD,uCAAuC;gBACvC,MAAMM,KAAK7B,MAAMkB,MAAMlC,YAAYC,aAAaC;gBAChDoB,QAAQ,CAACY,IAAI,GAAG;oBAAEW;oBAAIP,OAAOQ,MAAMC,IAAI,CAACT;gBAAO;YACjD;QACF;IACF;IACA,KAAK,MAAMpC,UAAUD,YAAY+C,OAAO,CAAE;QACxC9C,OAAOsB,MAAM,CAACC,OAAO,CAACF;IACxB;IAEAD,WAAW2B,OAAOC,IAAI,CAAC5B,UACpB6B,IAAI,EACL,wCAAwC;KACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGhC,QAAQ,CAACgC,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAEhD,OAAO;QACLnC,uBAAuBI;QACvBH,oBAAoB2B,MAAMC,IAAI,CAAC3B;IACjC;AACF;AAEO,MAAMrB;IAOXwD,YAAYC,IAMX,CAAE;QACD,IAAI,CAACC,QAAQ,GAAGD,KAAKC,QAAQ;QAC7B,IAAI,CAACC,aAAa,GAAGF,KAAKG,QAAQ,IAAIH,KAAKI,MAAM;QACjD,IAAI,CAACC,UAAU,GAAGC,QAAQN,KAAKG,QAAQ;QACvC,IAAI,CAACI,YAAY,GAAGP,KAAKO,YAAY;QACrC,IAAI,CAAC/C,GAAG,GAAGwC,KAAKxC,GAAG;IACrB;IAEAgD,aAAaC,QAAa,EAAEhE,WAAgB,EAAE;QAC5C,MAAMc,gBAAgB,IAAI,CAAC2C,aAAa,GACpCvB,aAAI,CAAC+B,OAAO,CAAC,IAAI,CAACR,aAAa,IAC/BS;QACJ,MAAMlD,iCAAiC,CAAC,IAAI,CAACD,GAAG,IAAI,IAAI,CAAC6C,UAAU;QACnE,MAAM,EAAE3C,qBAAqB,EAAEC,kBAAkB,EAAE,GAAGN,cACpDoD,UACAhE,aACAc,eACA,IAAI,CAACC,GAAG,EACRC;QAGFhB,YAAYmE,SAAS,CACnB,IAAI,CAACX,QAAQ,EACb,IAAIY,gBAAO,CAACC,SAAS,CAACC,KAAKC,SAAS,CAACtD,uBAAuB,MAAM;QAGpE,IAAI,IAAI,CAAC6C,YAAY,EAAE;YACrB9D,YAAYmE,SAAS,CACnB,IAAI,CAACL,YAAY,EACjB,IAAIM,gBAAO,CAACC,SAAS,CACnB,CAAC,+BAA+B,EAAEC,KAAKC,SAAS,CAC9CD,KAAKC,SAAS,CAACtD,yBACd;QAGT;QAEA,2EAA2E;QAC3E,4EAA4E;QAC5E,sDAAsD;QACtD,IAAID,gCAAgC;YAClChB,YAAYmE,SAAS,CACnB,GAAGK,+BAAoB,CAAC,KAAK,CAAC,EAC9B,IAAIJ,gBAAO,CAACC,SAAS,CAACC,KAAKC,SAAS,CAACrD,oBAAoB,MAAM;YAGjE,4BAA4B;YAC5BlB,YAAYmE,SAAS,CACnB,CAAC,OAAO,EAAEK,+BAAoB,CAAC,GAAG,CAAC,EACnC,IAAIJ,gBAAO,CAACC,SAAS,CACnB,CAAC,4BAA4B,EAAEC,KAAKC,SAAS,CAC3CD,KAAKC,SAAS,CAACrD,sBACd;QAGT;IACF;IAEAuD,MAAMT,QAA0B,EAAE;QAChCA,SAASU,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,yBAAyB,CAAC5E;YAChDA,YAAY0E,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEE,MAAM;gBACNC,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,IAAI,CAACnB,YAAY,CAACC,UAAUhE;YAC9B;QAEJ;IACF;AACF", "ignoreList": [0]}
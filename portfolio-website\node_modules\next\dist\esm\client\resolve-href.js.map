{"version": 3, "sources": ["../../src/client/resolve-href.ts"], "sourcesContent": ["import type { NextRouter, Url } from '../shared/lib/router/router'\n\nimport { searchParamsToUrlQuery } from '../shared/lib/router/utils/querystring'\nimport { formatWithValidation } from '../shared/lib/router/utils/format-url'\nimport { omit } from '../shared/lib/router/utils/omit'\nimport { normalizeRepeatedSlashes } from '../shared/lib/utils'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\nimport { isLocalURL } from '../shared/lib/router/utils/is-local-url'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\n\n/**\n * Resolves a given hyperlink with a certain router state (basePath not included).\n * Preserves absolute urls.\n */\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs: true\n): [string, string] | [string]\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: false\n): string\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: boolean\n): [string, string] | [string] | string {\n  // we use a dummy base url for relative urls\n  let base: URL\n  let urlAsString = typeof href === 'string' ? href : formatWithValidation(href)\n\n  // repeated slashes and backslashes in the URL are considered\n  // invalid and will never match a Next.js page/file\n  // https://www.rfc-editor.org/rfc/rfc3986.html#section-3.1\n  const urlProtoMatch = urlAsString.match(/^[a-z][a-z0-9+.-]*:\\/\\//i)\n  const urlAsStringNoProto = urlProtoMatch\n    ? urlAsString.slice(urlProtoMatch[0].length)\n    : urlAsString\n\n  const urlParts = urlAsStringNoProto.split('?', 1)\n\n  if ((urlParts[0] || '').match(/(\\/\\/|\\\\)/)) {\n    console.error(\n      `Invalid href '${urlAsString}' passed to next/router in page: '${router.pathname}'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.`\n    )\n    const normalizedUrl = normalizeRepeatedSlashes(urlAsStringNoProto)\n    urlAsString = (urlProtoMatch ? urlProtoMatch[0] : '') + normalizedUrl\n  }\n\n  // Return because it cannot be routed by the Next.js router\n  if (!isLocalURL(urlAsString)) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n\n  try {\n    base = new URL(\n      urlAsString.startsWith('#') ? router.asPath : router.pathname,\n      'http://n'\n    )\n  } catch (_) {\n    // fallback to / for invalid asPath values e.g. //\n    base = new URL('/', 'http://n')\n  }\n\n  try {\n    const finalUrl = new URL(urlAsString, base)\n    finalUrl.pathname = normalizePathTrailingSlash(finalUrl.pathname)\n    let interpolatedAs = ''\n\n    if (\n      isDynamicRoute(finalUrl.pathname) &&\n      finalUrl.searchParams &&\n      resolveAs\n    ) {\n      const query = searchParamsToUrlQuery(finalUrl.searchParams)\n\n      const { result, params } = interpolateAs(\n        finalUrl.pathname,\n        finalUrl.pathname,\n        query\n      )\n\n      if (result) {\n        interpolatedAs = formatWithValidation({\n          pathname: result,\n          hash: finalUrl.hash,\n          query: omit(query, params),\n        })\n      }\n    }\n\n    // if the origin didn't change, it means we received a relative href\n    const resolvedHref =\n      finalUrl.origin === base.origin\n        ? finalUrl.href.slice(finalUrl.origin.length)\n        : finalUrl.href\n\n    return resolveAs\n      ? [resolvedHref, interpolatedAs || resolvedHref]\n      : resolvedHref\n  } catch (_) {\n    return resolveAs ? [urlAsString] : urlAsString\n  }\n}\n"], "names": ["searchParamsToUrlQuery", "formatWithValidation", "omit", "normalizeRepeatedSlashes", "normalizePathTrailingSlash", "isLocalURL", "isDynamicRoute", "interpolateAs", "resolveHref", "router", "href", "resolveAs", "base", "urlAsString", "urlProtoMatch", "match", "urlAsStringNoProto", "slice", "length", "urlParts", "split", "console", "error", "pathname", "normalizedUrl", "URL", "startsWith", "<PERSON><PERSON><PERSON>", "_", "finalUrl", "interpolatedAs", "searchParams", "query", "result", "params", "hash", "resolvedHref", "origin"], "mappings": "AAEA,SAASA,sBAAsB,QAAQ,yCAAwC;AAC/E,SAASC,oBAAoB,QAAQ,wCAAuC;AAC5E,SAASC,IAAI,QAAQ,kCAAiC;AACtD,SAASC,wBAAwB,QAAQ,sBAAqB;AAC9D,SAASC,0BAA0B,QAAQ,6BAA4B;AACvE,SAASC,UAAU,QAAQ,0CAAyC;AACpE,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,aAAa,QAAQ,4CAA2C;AAgBzE,OAAO,SAASC,YACdC,MAAkB,EAClBC,IAAS,EACTC,SAAmB;IAEnB,4CAA4C;IAC5C,IAAIC;IACJ,IAAIC,cAAc,OAAOH,SAAS,WAAWA,OAAOT,qBAAqBS;IAEzE,6DAA6D;IAC7D,mDAAmD;IACnD,0DAA0D;IAC1D,MAAMI,gBAAgBD,YAAYE,KAAK,CAAC;IACxC,MAAMC,qBAAqBF,gBACvBD,YAAYI,KAAK,CAACH,aAAa,CAAC,EAAE,CAACI,MAAM,IACzCL;IAEJ,MAAMM,WAAWH,mBAAmBI,KAAK,CAAC,KAAK;IAE/C,IAAI,AAACD,CAAAA,QAAQ,CAAC,EAAE,IAAI,EAAC,EAAGJ,KAAK,CAAC,cAAc;QAC1CM,QAAQC,KAAK,CACX,AAAC,mBAAgBT,cAAY,uCAAoCJ,OAAOc,QAAQ,GAAC;QAEnF,MAAMC,gBAAgBrB,yBAAyBa;QAC/CH,cAAc,AAACC,CAAAA,gBAAgBA,aAAa,CAAC,EAAE,GAAG,EAAC,IAAKU;IAC1D;IAEA,2DAA2D;IAC3D,IAAI,CAACnB,WAAWQ,cAAc;QAC5B,OAAQF,YAAY;YAACE;SAAY,GAAGA;IACtC;IAEA,IAAI;QACFD,OAAO,IAAIa,IACTZ,YAAYa,UAAU,CAAC,OAAOjB,OAAOkB,MAAM,GAAGlB,OAAOc,QAAQ,EAC7D;IAEJ,EAAE,OAAOK,GAAG;QACV,kDAAkD;QAClDhB,OAAO,IAAIa,IAAI,KAAK;IACtB;IAEA,IAAI;QACF,MAAMI,WAAW,IAAIJ,IAAIZ,aAAaD;QACtCiB,SAASN,QAAQ,GAAGnB,2BAA2ByB,SAASN,QAAQ;QAChE,IAAIO,iBAAiB;QAErB,IACExB,eAAeuB,SAASN,QAAQ,KAChCM,SAASE,YAAY,IACrBpB,WACA;YACA,MAAMqB,QAAQhC,uBAAuB6B,SAASE,YAAY;YAE1D,MAAM,EAAEE,MAAM,EAAEC,MAAM,EAAE,GAAG3B,cACzBsB,SAASN,QAAQ,EACjBM,SAASN,QAAQ,EACjBS;YAGF,IAAIC,QAAQ;gBACVH,iBAAiB7B,qBAAqB;oBACpCsB,UAAUU;oBACVE,MAAMN,SAASM,IAAI;oBACnBH,OAAO9B,KAAK8B,OAAOE;gBACrB;YACF;QACF;QAEA,oEAAoE;QACpE,MAAME,eACJP,SAASQ,MAAM,KAAKzB,KAAKyB,MAAM,GAC3BR,SAASnB,IAAI,CAACO,KAAK,CAACY,SAASQ,MAAM,CAACnB,MAAM,IAC1CW,SAASnB,IAAI;QAEnB,OAAOC,YACH;YAACyB;YAAcN,kBAAkBM;SAAa,GAC9CA;IACN,EAAE,OAAOR,GAAG;QACV,OAAOjB,YAAY;YAACE;SAAY,GAAGA;IACrC;AACF", "ignoreList": [0]}
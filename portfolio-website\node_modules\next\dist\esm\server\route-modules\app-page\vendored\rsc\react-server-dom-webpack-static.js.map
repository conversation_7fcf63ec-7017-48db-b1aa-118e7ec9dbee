{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-static.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactServerDOMWebpackStatic\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMWebpackStatic"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,2BAA2B", "ignoreList": [0]}
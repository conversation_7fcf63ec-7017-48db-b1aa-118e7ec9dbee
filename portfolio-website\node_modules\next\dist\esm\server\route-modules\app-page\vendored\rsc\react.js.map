{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0]}